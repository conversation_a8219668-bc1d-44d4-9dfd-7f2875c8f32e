#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
北京市道路尘负荷数据分析系统
当前支持丰台区和经开区，预留其他区域接口
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from datetime import datetime
import sys
import os
import re
import json

# 检查并导入python-docx库
try:
    from docx import Document
    from docx.shared import Inches, Pt, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    from docx.oxml.ns import qn
    from docx.oxml.shared import OxmlElement
except ImportError:
    print("错误：缺少python-docx库，请运行以下命令安装：")
    print("pip install python-docx")
    sys.exit(1)

# 图表和文件转换依赖
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib as mpl
    # 配置中文字体及负号正常显示
    mpl.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    mpl.rcParams['axes.unicode_minus'] = False
except ImportError:
    plt = None  # 运行时检测
    FigureCanvasTkAgg = None  # 运行时检测

# PDF导出依赖
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️ 注意：未安装reportlab库，PDF导出功能将不可用")

# 高级图表依赖
try:
    import seaborn as sns
    import plotly.graph_objects as go
    ADVANCED_CHARTS_AVAILABLE = True
except ImportError:
    ADVANCED_CHARTS_AVAILABLE = False
    print("⚠️ 注意：未安装seaborn/plotly库，高级图表功能将受限")

class Config:
    """配置管理类"""

    # 尘负荷等级阈值配置
    DUST_LEVEL_THRESHOLDS = {
        '优': 0.15,
        '良': 0.45,
        '中': 1.20
        # 大于1.20为'差'
    }

    # 异常值处理配置
    ABNORMAL_VALUE_CONFIG = {
        'zero_threshold': 0.00,  # 零值阈值
        'min_correction_value': 0.10,  # 最小修正值
        'min_threshold': 0.10  # 小于此值的修正为此值
    }

    # 文件配置
    FILE_CONFIG = {
        'road_filter_file': '道路匹配表.xlsx',
        'supported_image_formats': ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'),
        'max_poor_roads_display': 10  # 差等级道路最大显示数量
    }

    # 界面配置
    UI_CONFIG = {
        'window_size': "750x900",
        'log_height': 12,
        'street_combo_width': 18
    }

class ReportTemplate:
    """报告模板配置类"""

    def __init__(self, name, description=""):
        self.name = name
        self.description = description
        self.sections = []
        self.charts = []
        self.format_settings = {
            'font_name_chinese': '仿宋_GB2312',
            'font_name_english': 'Times New Roman',
            'title_size': 16,
            'heading_size': 14,
            'body_size': 12,
            'table_header_bg': 'D9D9D9',
            'line_spacing': 24,
            'first_line_indent': 480
        }

    def add_section(self, section_type, title, content_type="text", options=None):
        """添加报告章节"""
        section = {
            'type': section_type,  # 'heading', 'text', 'table', 'chart', 'image'
            'title': title,
            'content_type': content_type,
            'options': options or {}
        }
        self.sections.append(section)

    def add_chart(self, chart_type, title, options=None):
        """添加图表配置"""
        chart = {
            'type': chart_type,  # 'bar', 'pie', 'scatter', 'heatmap', 'radar', 'timeline'
            'title': title,
            'options': options or {}
        }
        self.charts.append(chart)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'name': self.name,
            'description': self.description,
            'sections': self.sections,
            'charts': self.charts,
            'format_settings': self.format_settings
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建模板"""
        template = cls(data['name'], data.get('description', ''))
        template.sections = data.get('sections', [])
        template.charts = data.get('charts', [])
        template.format_settings = data.get('format_settings', template.format_settings)
        return template

class ReportTemplateManager:
    """报告模板管理器"""

    def __init__(self):
        self.templates = {}
        self.current_template = None
        self.templates_dir = "report_templates"
        self.ensure_templates_dir()
        self.load_default_templates()

    def ensure_templates_dir(self):
        """确保模板目录存在"""
        if not os.path.exists(self.templates_dir):
            os.makedirs(self.templates_dir)

    def load_default_templates(self):
        """加载默认模板"""
        # 标准报告模板
        standard_template = ReportTemplate("标准报告", "包含完整分析内容的标准报告格式")
        standard_template.add_section('heading', '一、监测结果', 'summary')
        standard_template.add_section('heading', '二、监测道路汇总表', 'road_table')
        standard_template.add_section('heading', '三、街道监测统计', 'street_table')
        standard_template.add_section('heading', '四、差等级道路情况', 'poor_roads')
        standard_template.add_section('heading', '五、数据分析图表', 'charts')
        standard_template.add_chart('bar', '街道平均尘负荷对比')
        standard_template.add_chart('pie', '道路等级分布')
        standard_template.add_chart('scatter', '差等级道路分布')
        self.templates['标准报告'] = standard_template

        # 简化报告模板
        simple_template = ReportTemplate("简化报告", "包含核心数据的简化报告格式")
        simple_template.add_section('heading', '一、监测结果概要', 'summary')
        simple_template.add_section('heading', '二、监测道路汇总', 'road_table')
        simple_template.add_chart('bar', '街道平均尘负荷对比')
        simple_template.add_chart('pie', '道路等级分布')
        self.templates['简化报告'] = simple_template

        # 详细分析模板
        detailed_template = ReportTemplate("详细分析", "包含深度分析和多种图表的详细报告")
        detailed_template.add_section('heading', '一、监测结果概述', 'summary')
        detailed_template.add_section('heading', '二、数据质量评估', 'data_quality')
        detailed_template.add_section('heading', '三、监测道路汇总表', 'road_table')
        detailed_template.add_section('heading', '四、街道监测统计', 'street_table')
        detailed_template.add_section('heading', '五、差等级道路深度分析', 'poor_roads_detailed')
        detailed_template.add_section('heading', '六、时间趋势分析', 'time_analysis')
        detailed_template.add_section('heading', '七、综合分析图表', 'charts')
        detailed_template.add_chart('bar', '街道平均尘负荷对比')
        detailed_template.add_chart('pie', '道路等级分布')
        detailed_template.add_chart('scatter', '差等级道路分布')
        detailed_template.add_chart('heatmap', '街道-时间热力图')
        detailed_template.add_chart('radar', '街道综合评估雷达图')
        detailed_template.add_chart('timeline', '尘负荷时间序列')
        self.templates['详细分析'] = detailed_template



        # 设置默认模板
        self.current_template = standard_template

    def get_template_names(self):
        """获取所有模板名称"""
        return list(self.templates.keys())

    def get_template(self, name):
        """获取指定模板"""
        return self.templates.get(name)

    def set_current_template(self, name):
        """设置当前使用的模板"""
        if name in self.templates:
            self.current_template = self.templates[name]
            return True
        return False

    def save_template(self, template):
        """保存模板到文件"""
        try:
            file_path = os.path.join(self.templates_dir, f"{template.name}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, ensure_ascii=False, indent=2)
            self.templates[template.name] = template
            return True
        except Exception as e:
            print(f"保存模板失败: {e}")
            return False

    def load_template(self, file_path):
        """从文件加载模板"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            template = ReportTemplate.from_dict(data)
            self.templates[template.name] = template
            return template
        except Exception as e:
            print(f"加载模板失败: {e}")
            return None



class AdvancedChartGenerator:
    """高级图表生成器"""

    def __init__(self, data, config):
        self.data = data
        self.config = config
        self.charts = []

    def generate_heatmap(self, title="街道-时间热力图"):
        """生成热力图"""
        if not ADVANCED_CHARTS_AVAILABLE:
            return None

        try:
            # 准备数据：按街道和时间分组
            if '开始时间' in self.data.columns:
                self.data['时间'] = pd.to_datetime(self.data['开始时间']).dt.hour
                heatmap_data = self.data.groupby(['所属街道', '时间'])['尘负荷'].mean().unstack(fill_value=0)

                fig, ax = plt.subplots(figsize=(12, 8))
                sns.heatmap(heatmap_data, annot=True, fmt='.2f', cmap='YlOrRd', ax=ax)
                ax.set_title(title, fontsize=14, fontweight='bold')
                ax.set_xlabel('监测时间（小时）', fontsize=12)
                ax.set_ylabel('街道名称', fontsize=12)

                plt.tight_layout()
                return fig
        except Exception as e:
            print(f"生成热力图失败: {e}")
        return None

    def generate_radar_chart(self, title="街道综合评估雷达图"):
        """生成雷达图"""
        if not ADVANCED_CHARTS_AVAILABLE:
            return None

        try:
            # 计算各街道的多维指标
            street_stats = self.data.groupby('所属街道').agg({
                '尘负荷': ['mean', 'std', 'count']
            }).round(2)

            # 扁平化列名
            street_stats.columns = ['平均值', '标准差', '数据量']

            # 标准化数据到0-1范围
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            normalized_data = scaler.fit_transform(street_stats)

            # 创建雷达图
            fig = go.Figure()

            categories = ['平均尘负荷', '数据稳定性', '监测覆盖度']

            for i, street in enumerate(street_stats.index):
                values = normalized_data[i]
                # 数据稳定性取反（标准差越小越好）
                values[1] = 1 - values[1]

                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=categories,
                    fill='toself',
                    name=street
                ))

            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 1]
                    )),
                showlegend=True,
                title=title
            )

            # 保存为HTML文件
            html_path = f"radar_chart_{int(datetime.now().timestamp())}.html"
            fig.write_html(html_path)
            return html_path

        except Exception as e:
            print(f"生成雷达图失败: {e}")
        return None

    def generate_timeline_chart(self, title="尘负荷时间序列"):
        """生成时间序列图"""
        if not ADVANCED_CHARTS_AVAILABLE:
            return None

        try:
            if '开始时间' not in self.data.columns:
                return None

            # 按时间分组计算平均值
            time_data = self.data.copy()
            time_data['时间'] = pd.to_datetime(time_data['开始时间'])
            time_series = time_data.groupby(['时间', '所属街道'])['尘负荷'].mean().reset_index()

            fig = go.Figure()

            for street in time_series['所属街道'].unique():
                street_data = time_series[time_series['所属街道'] == street]
                fig.add_trace(go.Scatter(
                    x=street_data['时间'],
                    y=street_data['尘负荷'],
                    mode='lines+markers',
                    name=street,
                    line=dict(width=2),
                    marker=dict(size=6)
                ))

            fig.update_layout(
                title=title,
                xaxis_title='时间',
                yaxis_title='尘负荷 (克/平方米)',
                hovermode='x unified'
            )

            # 保存为HTML文件
            html_path = f"timeline_chart_{int(datetime.now().timestamp())}.html"
            fig.write_html(html_path)
            return html_path

        except Exception as e:
            print(f"生成时间序列图失败: {e}")
        return None

    def generate_3d_scatter(self, title="三维散点图"):
        """生成3D散点图"""
        if not ADVANCED_CHARTS_AVAILABLE:
            return None

        try:
            # 计算每个道路的统计指标
            road_stats = self.data.groupby(['所属街道', '道路名称']).agg({
                '尘负荷': ['mean', 'std', 'count']
            }).reset_index()

            # 扁平化列名
            road_stats.columns = ['街道', '道路', '平均尘负荷', '标准差', '数据量']

            fig = go.Figure(data=[go.Scatter3d(
                x=road_stats['平均尘负荷'],
                y=road_stats['标准差'],
                z=road_stats['数据量'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=road_stats['平均尘负荷'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="平均尘负荷")
                ),
                text=road_stats['道路'],
                hovertemplate='<b>%{text}</b><br>' +
                             '平均尘负荷: %{x:.2f}<br>' +
                             '标准差: %{y:.2f}<br>' +
                             '数据量: %{z}<br>' +
                             '<extra></extra>'
            )])

            fig.update_layout(
                title=title,
                scene=dict(
                    xaxis_title='平均尘负荷',
                    yaxis_title='标准差',
                    zaxis_title='数据量'
                )
            )

            # 保存为HTML文件
            html_path = f"3d_scatter_{int(datetime.now().timestamp())}.html"
            fig.write_html(html_path)
            return html_path

        except Exception as e:
            print(f"生成3D散点图失败: {e}")
        return None

class PDFReportGenerator:
    """PDF报告生成器"""

    def __init__(self, template_manager):
        self.template_manager = template_manager
        self.styles = None
        self.story = []

    def setup_styles(self):
        """设置PDF样式"""
        if not PDF_AVAILABLE:
            return False

        try:
            # 注册中文字体（如果可用）
            try:
                # 尝试注册系统中的中文字体
                font_paths = [
                    'C:/Windows/Fonts/simsun.ttc',  # 宋体
                    'C:/Windows/Fonts/simhei.ttf',  # 黑体
                    '/System/Library/Fonts/PingFang.ttc',  # macOS
                    '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'  # Linux
                ]

                for font_path in font_paths:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont('Chinese', font_path))
                        break
            except:
                pass  # 如果字体注册失败，使用默认字体

            # 创建样式
            self.styles = getSampleStyleSheet()

            # 检查中文字体是否可用
            registered_fonts = pdfmetrics.getRegisteredFontNames()
            chinese_font_available = 'Chinese' in registered_fonts

            # 标题样式
            self.styles.add(ParagraphStyle(
                name='ChineseTitle',
                parent=self.styles['Title'],
                fontName='Chinese' if chinese_font_available else 'Helvetica-Bold',
                fontSize=18,
                alignment=TA_CENTER,
                spaceAfter=20
            ))

            # 章节标题样式
            self.styles.add(ParagraphStyle(
                name='ChineseHeading',
                parent=self.styles['Heading1'],
                fontName='Chinese' if chinese_font_available else 'Helvetica-Bold',
                fontSize=14,
                spaceAfter=12
            ))

            # 正文样式
            self.styles.add(ParagraphStyle(
                name='ChineseBody',
                parent=self.styles['Normal'],
                fontName='Chinese' if chinese_font_available else 'Helvetica',
                fontSize=12,
                alignment=TA_JUSTIFY,
                firstLineIndent=24
            ))

            return True
        except Exception as e:
            print(f"设置PDF样式失败: {e}")
            return False

    def generate_pdf_report(self, data, output_path, template_name="标准报告"):
        """生成PDF报告"""
        if not PDF_AVAILABLE:
            raise Exception("PDF功能不可用，请安装reportlab库")

        if not self.setup_styles():
            raise Exception("PDF样式设置失败")

        template = self.template_manager.get_template(template_name)
        if not template:
            raise Exception(f"未找到模板: {template_name}")

        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            self.story = []

            # 添加标题
            title = f"北京市道路尘负荷监测结果报告"
            self.story.append(Paragraph(title, self.styles['ChineseTitle']))
            self.story.append(Spacer(1, 20))

            # 根据模板生成内容
            for section in template.sections:
                self._add_section_to_pdf(section, data)

            # 构建PDF
            doc.build(self.story)
            return True

        except Exception as e:
            self.log_message(f"❌ 生成PDF报告失败: {e}")
            return False

    def _add_section_to_pdf(self, section, data):
        """添加章节到PDF"""
        try:
            # 添加章节标题
            if section['title']:
                self.story.append(Paragraph(section['title'], self.styles['ChineseHeading']))
                self.story.append(Spacer(1, 12))

            # 根据内容类型添加内容
            if section['content_type'] == 'summary':
                self._add_summary_content(data)
            elif section['content_type'] == 'road_table':
                self._add_road_table(data)
            elif section['content_type'] == 'street_table':
                self._add_street_table(data)
            elif section['content_type'] == 'poor_roads':
                self._add_poor_roads_content(data)
            elif section['content_type'] == 'charts':
                self._add_charts_placeholder()

            self.story.append(Spacer(1, 20))

        except Exception as e:
            self.log_message(f"❌ 添加章节失败: {e}")

    def _add_summary_content(self, data):
        """添加摘要内容"""
        try:
            total_roads = len(data)
            level_counts = data['等级'].value_counts() if '等级' in data.columns else {}

            content = f"本次共监测道路{total_roads}条。"
            for level in ['优', '良', '中', '差']:
                count = level_counts.get(level, 0)
                percentage = (count / total_roads * 100) if total_roads > 0 else 0
                content += f"其中{level}级道路{count}条，占比{percentage:.1f}%；"

            content = content.rstrip('；') + "。"

            self.story.append(Paragraph(content, self.styles['ChineseBody']))

        except Exception as e:
            self.log_message(f"❌ 添加摘要内容失败: {e}")

    def _add_road_table(self, data):
        """添加道路表格"""
        try:
            if data.empty:
                return

            # 准备表格数据
            table_data = [['序号', '道路名称', '街道名称', '尘负荷', '等级']]

            for idx, (_, row) in enumerate(data.head(20).iterrows(), 1):  # 限制显示前20条
                table_data.append([
                    str(idx),
                    str(row.get('道路名称', '')),
                    str(row.get('所属街道', '')),
                    f"{row.get('尘负荷', 0):.2f}",
                    str(row.get('等级', ''))
                ])

            # 创建表格
            table = Table(table_data, colWidths=[1*cm, 4*cm, 3*cm, 2*cm, 1.5*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            self.story.append(table)

        except Exception as e:
            print(f"添加道路表格失败: {e}")

    def _add_street_table(self, data):
        """添加街道统计表格"""
        try:
            if data.empty or '所属街道' not in data.columns:
                return

            # 计算街道统计
            street_stats = data.groupby('所属街道').agg({
                '道路名称': 'count',
                '尘负荷': 'mean'
            }).reset_index()
            street_stats.columns = ['街道名称', '道路数量', '平均尘负荷']

            # 准备表格数据
            table_data = [['街道名称', '道路数量（条）', '平均尘负荷']]

            for _, row in street_stats.iterrows():
                table_data.append([
                    str(row['街道名称']),
                    str(row['道路数量']),
                    f"{row['平均尘负荷']:.2f}"
                ])

            # 创建表格
            table = Table(table_data, colWidths=[4*cm, 3*cm, 3*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            self.story.append(table)

        except Exception as e:
            print(f"添加街道表格失败: {e}")

    def _add_poor_roads_content(self, data):
        """添加差等级道路内容"""
        try:
            if '等级' not in data.columns:
                return

            poor_roads = data[data['等级'] == '差']
            if poor_roads.empty:
                content = "本次监测中未发现差等级道路。"
            else:
                poor_count = len(poor_roads)
                total_roads = len(data)
                percentage = (poor_count / total_roads * 100) if total_roads > 0 else 0
                content = f"差等级道路共{poor_count}条，占比{percentage:.1f}%。"

                if poor_count <= 10:
                    road_names = "、".join(poor_roads['道路名称'].tolist())
                    content += f"具体包括：{road_names}。"

            self.story.append(Paragraph(content, self.styles['ChineseBody']))

        except Exception as e:
            self.log_message(f"❌ 添加差等级道路内容失败: {e}")

    def _add_charts_placeholder(self):
        """添加图表占位符"""
        content = "注：图表内容请参考单独生成的图表文件。"
        self.story.append(Paragraph(content, self.styles['ChineseBody']))

class MultiSelectCombobox(ttk.Frame):
    """带复选框的多选下拉框组件"""

    def __init__(self, parent, width=30, **kwargs):
        super().__init__(parent, **kwargs)

        self.width = width
        self.values = []
        self.selected_values = set()
        self.var = tk.StringVar()
        self.dropdown_open = False

        # 创建主要的下拉框样式组件
        self.combobox = ttk.Combobox(self, textvariable=self.var,
                                   state="readonly", width=width,
                                   style='Modern.TCombobox',
                                   font=("微软雅黑", 10))
        self.combobox.pack(fill=tk.X)

        # 绑定事件
        self.combobox.bind('<Button-1>', self.toggle_dropdown)
        self.combobox.bind('<FocusIn>', self.on_focus)

        # 创建下拉列表窗口（初始隐藏）
        self.dropdown_window = None

        self.update_display()

    def set_values(self, values):
        """设置可选值列表"""
        self.values = values
        self.selected_values.clear()
        self.update_display()
        if self.dropdown_window:
            self.create_dropdown_content()

    def get_selected(self):
        """获取选中的值"""
        return list(self.selected_values)

    def select_all(self):
        """全选"""
        self.selected_values = set(self.values)
        self.update_display()
        if self.dropdown_window:
            self.create_dropdown_content()

    def clear_selection(self):
        """清空选择"""
        self.selected_values.clear()
        self.update_display()
        if self.dropdown_window:
            self.create_dropdown_content()

    def update_display(self):
        """更新显示文本"""
        count = len(self.selected_values)
        if count == 0:
            self.var.set("请选择街道...")
        elif count == 1:
            selected_item = list(self.selected_values)[0]
            # 提取街道名称（去除数据量信息）
            street_name = selected_item.split(' (')[0]
            self.var.set(street_name)
        else:
            self.var.set(f"已选择 {count} 个街道")

    def toggle_dropdown(self, event=None):
        """切换下拉列表显示状态"""
        if self.dropdown_open:
            self.hide_dropdown()
        else:
            self.show_dropdown()

    def on_focus(self, event=None):
        """获得焦点时显示下拉列表"""
        if not self.dropdown_open:
            self.show_dropdown()

    def show_dropdown(self):
        """显示下拉列表"""
        if not self.values or self.dropdown_open:
            return

        self.dropdown_open = True

        # 创建顶层窗口
        self.dropdown_window = tk.Toplevel(self)
        self.dropdown_window.wm_overrideredirect(True)
        self.dropdown_window.configure(bg='white', relief='solid', bd=1)

        # 计算位置
        x = self.combobox.winfo_rootx()
        y = self.combobox.winfo_rooty() + self.combobox.winfo_height()
        self.dropdown_window.geometry(f"+{x}+{y}")

        # 创建下拉内容
        self.create_dropdown_content()

        # 绑定失去焦点事件
        self.dropdown_window.bind('<FocusOut>', self.on_dropdown_focus_out)
        self.dropdown_window.focus_set()

    def create_dropdown_content(self):
        """创建下拉列表内容（Treeview 版本）"""
        if not self.dropdown_window:
            return

        # 清空现有内容
        for widget in self.dropdown_window.winfo_children():
            widget.destroy()

        # --- 搜索框 ---
        search_frame = ttk.Frame(self.dropdown_window)
        search_frame.pack(fill=tk.X, padx=6, pady=(6, 4))

        ttk.Label(search_frame, text="🔍").pack(side=tk.LEFT)
        self._search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self._search_var, width=20)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self._search_var.trace_add("write", lambda *args: self.populate_tree())

        # --- 全选 / 清空 按钮 ---
        btn_frame = ttk.Frame(self.dropdown_window)
        btn_frame.pack(fill=tk.X, padx=6, pady=(0, 4))

        ttk.Button(btn_frame, text="✓ 全选", command=self.select_all_and_update,
                  style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 6))
        ttk.Button(btn_frame, text="✗ 清空", command=self.clear_all_and_update,
                  style='Modern.TButton').pack(side=tk.LEFT)

        # --- Treeview 区域 ---
        tree_frame = ttk.Frame(self.dropdown_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=6, pady=(0, 6))

        self.tree = ttk.Treeview(tree_frame, show='tree', selectmode='none', height=10)
        yscroll = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=yscroll.set)
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        yscroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 行点击事件
        self.tree.bind('<Button-1>', self.on_tree_item_click)

        # 初次填充
        self.populate_tree()

        # 调整窗口大小
        width = max(self.combobox.winfo_width(), 260)
        height = 320  # 固定高度
        self.dropdown_window.geometry(f"{width}x{height}")

    # ---------- Treeview 辅助方法 ----------
    def populate_tree(self):
        """根据过滤词刷新 Treeview"""
        if not hasattr(self, 'tree'):
            return

        filter_text = getattr(self, '_search_var', tk.StringVar()).get().strip().lower()

        # 清空现有行
        for iid in self.tree.get_children():
            self.tree.delete(iid)

        for value in self.values:
            if filter_text and filter_text not in value.lower():
                continue
            checked = '☑' if value in self.selected_values else '☐'
            display = f"{checked} {value}"
            self.tree.insert('', 'end', iid=value, text=display)

    def on_tree_item_click(self, event):
        """点击行切换选中状态"""
        item_id = self.tree.identify_row(event.y)
        if not item_id:
            return
        value = item_id
        if value in self.selected_values:
            self.selected_values.remove(value)
        else:
            self.selected_values.add(value)
        self.update_display()
        self.populate_tree()

    # ---------- 覆盖全选/清空以同步 Treeview ----------
    def select_all_and_update(self):
        """全选并刷新"""
        self.selected_values = set(self.values)
        self.update_display()
        self.populate_tree()

    def clear_all_and_update(self):
        """清空并刷新"""
        self.selected_values.clear()
        self.update_display()
        self.populate_tree()

    def hide_dropdown(self):
        """隐藏下拉列表"""
        if self.dropdown_window:
            self.dropdown_window.destroy()
            self.dropdown_window = None
        self.dropdown_open = False

    def on_dropdown_focus_out(self, event=None):
        """下拉列表失去焦点时隐藏"""
        # 延迟隐藏，避免点击复选框时立即关闭
        self.after(100, self.hide_dropdown)

class BeijingRoadDustAnalyzer:
    def __init__(self, root):
        """初始化程序主界面"""
        self.root = root
        self.root.title("北京市道路尘负荷数据分析系统")

        # 先隐藏主窗口，避免闪烁
        self.root.withdraw()

        # 设置窗口属性
        self.root.resizable(True, True)

        # 设置窗口图标
        try:
            if os.path.exists('app_icon.ico'):
                self.root.iconbitmap('app_icon.ico')
        except:
            pass  # 如果图标加载失败，继续运行

        # 设置窗口背景色
        self.root.configure(bg='#f5f5f5')

        # 初始化样式系统
        self.setup_styles()

        # 支持的区域及其街道列表
        self.district_streets = {
            "丰台区": [
                "北宫镇", "成寿寺街道", "大红门街道", "东高地街道", "东铁匠营街道",
                "方庄街道", "丰台街道", "和义街道", "花乡街道", "看丹街道",
                "丽泽商务区", "六里桥街道", "卢沟桥街道", "马家堡街道", "南苑街道",
                "青塔街道", "石榴庄街道", "太平桥街道", "宛平街道", "王佐镇",
                "五里店街道", "西罗园街道", "新村街道", "右安门街道", "玉泉营街道",
                "云岗街道", "长辛店街道"
            ],
            "经开区": [
                "荣华街道", "博兴街道", "亦庄镇", "瀛海镇", "台湖镇"
            ]
            # 预留其他区域接口
            # "朝阳区": [],
            # "海淀区": [],
            # ...
        }

        # 数据变量
        self.df = None
        self.selected_file = None
        self.selected_district = None
        self.available_streets = []
        self.street_data_count = {}
        self.road_filter_df = None  # 道路匹配表数据
        self.allowed_roads = {}  # 按区域存储允许的道路名称集合

        # 报告功能增强
        self.template_manager = ReportTemplateManager()
        self.pdf_generator = PDFReportGenerator(self.template_manager)
        self.advanced_chart_generator = None

        # 创建界面
        self.create_widgets()

        # 加载道路匹配表
        try:
            self.load_road_filter()
        except Exception as e:
            self.log_message(f"❌ 道路匹配表加载失败: {e}")



        # 初始化欢迎信息
        self.log_message("✅ 北京市道路尘负荷数据分析系统已启动")
        self.log_message("📋 请加载Excel数据表后开始分析")

    def validate_data_format(self, df):
        """验证数据格式

        Args:
            df: 数据框

        Returns:
            tuple: (is_valid, error_messages)
        """
        error_messages = []

        # 检查必要的列
        required_columns = ['道路名称', '所属街道', '尘负荷', '开始时间']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            error_messages.append(f"缺少必要的列: {', '.join(missing_columns)}")

        # 检查数据类型
        if '尘负荷' in df.columns:
            try:
                pd.to_numeric(df['尘负荷'], errors='coerce')
            except:
                error_messages.append("尘负荷列包含无法转换为数字的数据")

        # 检查数据量
        if len(df) == 0:
            error_messages.append("数据文件为空")
        elif len(df) < 10:
            error_messages.append("数据量过少，可能影响分析结果")

        # 检查尘负荷值范围
        if '尘负荷' in df.columns:
            dust_values = pd.to_numeric(df['尘负荷'], errors='coerce')
            if dust_values.max() > 100:
                error_messages.append("检测到异常大的尘负荷值，请检查数据单位")
            if dust_values.min() < 0:
                error_messages.append("检测到负数尘负荷值，请检查数据质量")

        return len(error_messages) == 0, error_messages

    def smart_street_match(self, analysis_street, reference_streets):
        """智能街道名称匹配

        Args:
            analysis_street: 分析结果中的街道名称
            reference_streets: 道路匹配表中的街道名称列表

        Returns:
            匹配的标准街道名称，如果无法匹配则返回None
        """
        if not analysis_street or pd.isna(analysis_street):
            return None

        analysis_street = str(analysis_street).strip()

        # 第一步：精确匹配
        if analysis_street in reference_streets:
            return analysis_street

        # 第二步：去除末尾数字后匹配
        # 匹配模式：街道名称后跟数字，如"丰台街道1", "马家堡街道2"
        base_name = re.sub(r'\d+$', '', analysis_street).strip()
        if base_name != analysis_street and base_name in reference_streets:
            if hasattr(self, 'log_message'):
                self.log_message(f"🔄 街道名称匹配: '{analysis_street}' → '{base_name}'")
            else:
                print(f"🔄 街道名称匹配: '{analysis_street}' → '{base_name}'")
            return base_name

        # 第三步：包含匹配（分析结果包含参考街道名称）
        for ref_street in reference_streets:
            if ref_street and analysis_street.startswith(str(ref_street)):
                if hasattr(self, 'log_message'):
                    self.log_message(f"🔄 街道名称包含匹配: '{analysis_street}' → '{ref_street}'")
                else:
                    print(f"🔄 街道名称包含匹配: '{analysis_street}' → '{ref_street}'")
                return ref_street

        # 第四步：反向包含匹配（参考街道名称包含分析结果）
        for ref_street in reference_streets:
            if ref_street and str(ref_street).startswith(analysis_street):
                if hasattr(self, 'log_message'):
                    self.log_message(f"🔄 街道名称反向匹配: '{analysis_street}' → '{ref_street}'")
                else:
                    print(f"🔄 街道名称反向匹配: '{analysis_street}' → '{ref_street}'")
                return ref_street

        # 无法匹配
        if hasattr(self, 'log_message'):
            self.log_message(f"⚠️ 无法匹配街道名称: '{analysis_street}'")
        else:
            print(f"⚠️ 无法匹配街道名称: '{analysis_street}'")
        return None

    def auto_detect_district(self, data_streets):
        """自动检测数据所属的区域

        Args:
            data_streets: 数据中的街道名称列表

        Returns:
            检测到的区域名称，如果无法确定则返回None
        """
        district_matches = {}

        # 统计每个区域匹配到的街道数量
        for district, reference_streets in self.district_streets.items():
            matched_count = 0
            matched_streets = []

            for data_street in data_streets:
                matched_street = self.smart_street_match(data_street, reference_streets)
                if matched_street:
                    matched_count += 1
                    matched_streets.append(f"{data_street} → {matched_street}")

            if matched_count > 0:
                district_matches[district] = {
                    'count': matched_count,
                    'streets': matched_streets
                }

        if not district_matches:
            print("⚠️ 未检测到任何已知区域的街道")
            return None

        # 选择匹配街道数量最多的区域
        best_district = max(district_matches.keys(), key=lambda d: district_matches[d]['count'])

        print(f"\n🎯 自动区域检测结果：")
        for district, info in district_matches.items():
            status = "✅ [已选择]" if district == best_district else "  "
            print(f"{status} {district}: 匹配到 {info['count']} 个街道")
            if district == best_district:
                for street_match in info['streets'][:5]:  # 只显示前5个
                    print(f"    • {street_match}")
                if len(info['streets']) > 5:
                    print(f"    • ... 还有 {len(info['streets']) - 5} 个街道")

        print(f"\n🏙️ 自动选择区域: {best_district}")
        return best_district

    def setup_styles(self):
        """设置自定义样式"""
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 自定义LabelFrame样式
        style.configure('Card.TLabelframe',
                       background='#ffffff',
                       borderwidth=1,
                       relief='solid',
                       bordercolor='#e0e0e0')
        style.configure('Card.TLabelframe.Label',
                       background='#ffffff',
                       foreground='#2d3748',
                       font=('微软雅黑', 10, 'bold'))

        # 自定义按钮样式
        style.configure('Modern.TButton',
                       background='#4a5568',
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('微软雅黑', 9))
        style.map('Modern.TButton',
                 background=[('active', '#2d3748'),
                           ('pressed', '#1a202c')])

        # 自定义Entry样式
        style.configure('Modern.TEntry',
                       borderwidth=1,
                       relief='solid',
                       bordercolor='#e0e0e0',
                       focuscolor='#4a5568')

        # 自定义Combobox样式
        style.configure('Modern.TCombobox',
                       borderwidth=1,
                       relief='solid',
                       bordercolor='#e0e0e0')

    def load_road_filter(self):
        """加载道路匹配表"""
        try:
            # 获取程序所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            road_filter_path = os.path.join(script_dir, '道路匹配表.xlsx')

            # 如果程序目录中没有文件，尝试当前工作目录
            if not os.path.exists(road_filter_path):
                road_filter_path = '道路匹配表.xlsx'

            self.road_filter_df = pd.read_excel(road_filter_path)

            # 初始化每个区域的允许道路集合
            for district in self.district_streets.keys():
                # 直接从道路匹配表中筛选该区域的道路
                district_roads = self.road_filter_df[
                    self.road_filter_df['街道名称'].isin(self.district_streets[district])
                ]
                self.allowed_roads[district] = set(district_roads['道路名称'].dropna().unique())
                self.log_message(f"✅ {district}加载成功，共{len(self.allowed_roads[district])}条允许的道路")
        except Exception as e:
            self.log_message(f"⚠️ 无法加载道路匹配表: {e}")
            self.log_message("将使用所有道路数据生成报告")
            self.road_filter_df = None
            self.allowed_roads = {district: set() for district in self.district_streets.keys()}

    def remove_duplicate_roads(self, df):
        """
        处理重复道路：对于同一道路名称在不同街道的情况，
        只保留数据量最多的街道的道路记录
        """
        if df.empty:
            return df

        # 统计每个道路在每个街道的数据量
        road_street_counts = df.groupby(['道路名称', '所属街道']).size().reset_index(name='数据量')

        # 找出每个道路数据量最多的街道
        max_count_roads = road_street_counts.loc[
            road_street_counts.groupby('道路名称')['数据量'].idxmax()
        ]

        # 创建保留的道路-街道组合集合
        keep_combinations = set(
            zip(max_count_roads['道路名称'], max_count_roads['所属街道'])
        )

        # 筛选数据，只保留数据量最多的街道的道路
        mask = df.apply(lambda row: (row['道路名称'], row['所属街道']) in keep_combinations, axis=1)
        result_df = df[mask].copy()

        # 统计处理效果
        duplicate_roads = road_street_counts.groupby('道路名称').size()
        duplicate_road_names = duplicate_roads[duplicate_roads > 1].index.tolist()

        if duplicate_road_names:
            self.log_message(f"发现{len(duplicate_road_names)}条重复道路，已处理：")
            for road_name in duplicate_road_names[:10]:  # 只显示前10个
                road_data = road_street_counts[road_street_counts['道路名称'] == road_name]
                max_street = road_data.loc[road_data['数据量'].idxmax(), '所属街道']
                max_count = road_data['数据量'].max()
                other_streets = road_data[road_data['所属街道'] != max_street]['所属街道'].tolist()
                self.log_message(f"  • {road_name}: 保留{max_street}({max_count}条), 剔除{other_streets}")

            if len(duplicate_road_names) > 10:
                self.log_message(f"  ... 还有{len(duplicate_road_names) - 10}条重复道路已处理")

        removed_records = len(df) - len(result_df)
        self.log_message(f"重复道路处理：{len(df)} → {len(result_df)} 条记录（剔除{removed_records}条）")

        return result_df

    def set_cell_background_color(self, cell, color_hex):
        """设置单元格背景色

        Args:
            cell: 单元格对象
            color_hex: 十六进制颜色代码（不含#）
        """
        try:
            # 获取单元格的属性
            tc = cell._tc
            tcPr = tc.get_or_add_tcPr()

            # 创建shd元素
            shd = OxmlElement('w:shd')
            shd.set(qn('w:val'), 'clear')
            shd.set(qn('w:color'), 'auto')
            shd.set(qn('w:fill'), color_hex)

            # 添加到单元格属性
            tcPr.append(shd)
        except Exception as e:
            print(f"设置单元格背景色失败: {e}")

    def set_cell_font(self, cell):
        """设置单元格字体

        Args:
            cell: 单元格对象
        """
        try:
            paragraph = cell.paragraphs[0]
            if paragraph.runs:
                run = paragraph.runs[0]
                run.font.name = 'Times New Roman'  # 英文数字字体
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')  # 中文字体
                run.font.size = Pt(10.5)  # 五号字体
                run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
        except Exception as e:
            print(f"设置单元格字体失败: {e}")

    def set_cell_vertical_alignment_and_spacing(self, cell):
        """设置单元格垂直居中和行距

        Args:
            cell: 单元格对象
        """
        try:
            # 设置垂直居中
            tc = cell._tc
            tcPr = tc.get_or_add_tcPr()
            vAlign = OxmlElement('w:vAlign')
            vAlign.set(qn('w:val'), 'center')
            tcPr.append(vAlign)

            # 设置段落行距为固定值24磅
            paragraph = cell.paragraphs[0]
            pPr = paragraph._element.get_or_add_pPr()

            # 创建spacing元素
            spacing = OxmlElement('w:spacing')
            spacing.set(qn('w:line'), '480')  # 24磅 = 480 twips (1磅 = 20 twips)
            spacing.set(qn('w:lineRule'), 'exact')  # 固定值
            pPr.append(spacing)

        except Exception as e:
            print(f"设置单元格垂直居中和行距失败: {e}")

    def get_dust_level(self, dust_value):
        """根据尘负荷值判定等级

        Args:
            dust_value: 尘负荷值

        Returns:
            等级字符串：优/良/中/差
        """
        if pd.isna(dust_value):
            return "未知"

        thresholds = Config.DUST_LEVEL_THRESHOLDS
        if dust_value <= thresholds['优']:
            return "优"
        elif dust_value <= thresholds['良']:
            return "良"
        elif dust_value <= thresholds['中']:
            return "中"
        else:
            return "差"

    def process_abnormal_dust_values(self, df):
        """处理异常尘负荷值

        Args:
            df: 数据框

        Returns:
            处理后的数据框和统计信息
        """
        if df.empty:
            return df, {"removed_zero": 0, "corrected_small": 0}

        original_count = len(df)
        config = Config.ABNORMAL_VALUE_CONFIG

        # 1. 去除保留两位小数后为0.00的值
        zero_mask = df['尘负荷'].round(2) == config['zero_threshold']
        zero_count = zero_mask.sum()
        df_filtered = df[~zero_mask].copy()

        # 2. 将小于阈值的值修改为最小值
        small_mask = df_filtered['尘负荷'] < config['min_threshold']
        small_count = small_mask.sum()
        df_filtered.loc[small_mask, '尘负荷'] = config['min_correction_value']

        # 统计信息
        stats = {
            "removed_zero": zero_count,
            "corrected_small": small_count,
            "original_count": original_count,
            "final_count": len(df_filtered)
        }

        if zero_count > 0 or small_count > 0:
            self.log_message(f"异常尘负荷值处理：")
            if zero_count > 0:
                self.log_message(f"  • 去除0.00值记录: {zero_count}条")
            if small_count > 0:
                self.log_message(f"  • 修正小于0.10的值: {small_count}条 → 0.10")
            self.log_message(f"  • 数据记录: {original_count} → {len(df_filtered)}条")

        return df_filtered, stats

    def get_poor_road_reasons(self):
        """获取差等级道路的随机原因（基于道路扬尘领域专业分析）"""
        import random

        # 污染源因素
        pollution_sources = [
            "周边建筑工地、拆迁工地扬尘污染严重，大量粉尘沉降至道路表面，导致尘负荷值显著升高",
            "道路两侧存在大面积裸露土地和未绿化区域，在风力作用下持续产生扬尘污染",
            "重型货车、渣土车等车辆通行频繁，车辆轮胎带泥、车身不洁造成严重的二次污染",
            "周边工业企业粉尘排放影响，颗粒物在大气中传输后沉降至道路表面"
        ]

        # 道路状况因素
        road_conditions = [
            "道路表面破损严重，存在明显坑洼和裂缝，雨后积水干涸形成大量泥垢沉积",
            "路面材质老化，表面粗糙度增加，微观结构变化导致尘土易于积累且难以清除",
            "道路排水系统设计不完善或维护不当，雨水冲刷带来的泥沙无法及时排出",
            "路面标线、井盖周边等细节部位清扫盲区较多，长期积累形成污染源"
        ]

        # 清扫保洁因素
        cleaning_factors = [
            "机械化清扫频次明显不足，人工清扫效率低下，无法及时清除新增的道路污染",
            "清扫作业时间安排不当，未能在交通高峰前完成清扫，清扫效果大打折扣",
            "洒水抑尘措施执行不到位，干扫作业过程中产生二次扬尘，加剧污染程度",
            "清扫设备老化或操作不规范，清扫质量不达标，残留污染物较多"
        ]

        # 环境气象因素
        environmental_factors = [
            "春季大风天气频发且持续时间长，风速过大时地表扬尘现象严重",
            "近期降水量偏少，空气湿度持续偏低，气象条件极利于尘土飞扬和传播",
            "周边绿化覆盖率不足，缺乏有效的天然防风屏障和尘土沉降缓冲区",
            "城市热岛效应影响，局部气流紊乱加剧了扬尘的产生和传输"
        ]

        # 从各类因素中随机选择
        all_factors = [pollution_sources, road_conditions, cleaning_factors, environmental_factors]
        selected_reasons = []

        # 确保至少从2个不同类别中选择原因
        selected_categories = random.sample(all_factors, random.randint(2, 3))

        for category in selected_categories:
            selected_reasons.append(random.choice(category))

        return "；".join(selected_reasons) + "。"

    def get_monitoring_time_range(self, data):
        """获取监测时间范围"""
        try:
            times = pd.to_datetime(data['开始时间'])
            start_time = times.min().strftime('%Y年%m月%d日')
            end_time = times.max().strftime('%Y年%m月%d日')
            if start_time == end_time:
                return start_time
            else:
                return f"{start_time}至{end_time}"
        except:
            return "2024年监测期间"

    def set_heading_style(self, heading):
        """设置标题样式"""
        if heading.runs:
            run = heading.runs[0]
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            run.font.size = Pt(14)
            run.font.color.rgb = RGBColor(0, 0, 0)

    def set_paragraph_style(self, paragraph):
        """设置段落样式：首行缩进2字符，行距固定值24磅"""
        # 设置字体
        if paragraph.runs:
            run = paragraph.runs[0]
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            run.font.size = Pt(12)
            run.font.color.rgb = RGBColor(0, 0, 0)

        # 设置段落格式
        try:
            pPr = paragraph._element.get_or_add_pPr()

            # 设置首行缩进2字符（24磅 = 2字符）
            ind = OxmlElement('w:ind')
            ind.set(qn('w:firstLine'), '480')  # 24磅 = 480 twips
            pPr.append(ind)

            # 设置行距为固定值24磅
            spacing = OxmlElement('w:spacing')
            spacing.set(qn('w:line'), '480')  # 24磅 = 480 twips
            spacing.set(qn('w:lineRule'), 'exact')  # 固定值
            pPr.append(spacing)

        except Exception as e:
            self.log_message(f"❌ 设置段落格式失败: {e}")

    def on_photos_option_changed(self):
        """照片选项变化事件处理"""
        if self.import_photos_var.get():
            self.photos_folder_entry.config(state="normal")
            self.browse_photos_button.config(state="normal")
        else:
            self.photos_folder_entry.config(state="disabled")
            self.browse_photos_button.config(state="disabled")
            self.photos_folder_var.set("")

    def browse_photos_folder(self):
        """浏览照片文件夹"""
        folder_path = filedialog.askdirectory(title="选择照片文件夹")
        if folder_path:
            self.photos_folder_var.set(folder_path)

    def get_save_file_path(self, selected_streets):
        """获取保存文件路径（通过另存为对话框或默认路径）"""
        from datetime import datetime
        current_date = datetime.now().strftime("%Y.%m.%d")

        if self.use_save_dialog_var.get():
            # 使用另存为对话框
            street_names_short = "、".join(selected_streets[:2])
            if len(selected_streets) > 2:
                street_names_short += f"等{len(selected_streets)}个街道"

            # 生成默认文件名
            default_filename = f"北京市{self.selected_district}尘负荷监测结果报告（{current_date}）.docx"
            # 处理文件名中的特殊字符
            default_filename = default_filename.replace('/', '_').replace('\\', '_').replace(':', '_').replace('?', '_').replace('*', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')

            # 弹出另存为对话框
            file_path = filedialog.asksaveasfilename(
                title="保存报告",
                defaultextension=".docx",
                filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")],
                initialfile=default_filename
            )

            if file_path:
                return file_path, os.path.dirname(file_path), os.path.basename(file_path)
            else:
                return None, None, None  # 用户取消了保存
        else:
            # 使用默认保存到程序目录
            street_names_short = "、".join(selected_streets[:3])
            if len(selected_streets) > 3:
                street_names_short += f"等{len(selected_streets)}个街道"

            # 生成基础文件名
            base_filename = f"北京市{self.selected_district}尘负荷监测结果报告_{current_date.replace('.', '')}_{street_names_short}"
            # 处理文件名中的特殊字符
            base_filename = base_filename.replace('/', '_').replace('\\', '_').replace(':', '_').replace('?', '_').replace('*', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
            output_filename = f"{base_filename}.docx"

            return output_filename, "程序运行目录", output_filename

    def get_image_files(self, folder_path):
        """获取文件夹中的图片文件"""
        if not folder_path or not os.path.exists(folder_path):
            return []

        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff')
        image_files = []

        try:
            for file in os.listdir(folder_path):
                if file.lower().endswith(image_extensions):
                    image_files.append(file)

            # 按文件名排序
            image_files.sort()
            return image_files
        except Exception as e:
            print(f"读取照片文件夹失败: {e}")
            return []

    def generate_image_title(self, filename):
        """根据文件名生成图片标题"""
        # 去除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 简单的标题生成规则
        if '道路' in name_without_ext or 'road' in name_without_ext.lower():
            return f"道路现状照片（{name_without_ext}）"
        elif '清扫' in name_without_ext or 'clean' in name_without_ext.lower():
            return f"清扫作业照片（{name_without_ext}）"
        elif '环境' in name_without_ext or 'environment' in name_without_ext.lower():
            return f"周边环境照片（{name_without_ext}）"
        elif '施工' in name_without_ext or 'construction' in name_without_ext.lower():
            return f"施工现场照片（{name_without_ext}）"
        else:
            return f"现场照片（{name_without_ext}）"

    def add_photos_to_document(self, doc):
        """在文档中添加照片"""
        if not self.import_photos_var.get():
            return 0

        photos_folder = self.photos_folder_var.get()
        if not photos_folder:
            print("未选择照片文件夹")
            return 0

        image_files = self.get_image_files(photos_folder)
        if not image_files:
            print("照片文件夹中没有找到图片文件")
            return 0

        # 添加分页符
        doc.add_page_break()

        # 添加附图标题
        appendix_title = doc.add_heading("附图：", level=2)
        self.set_heading_style(appendix_title)

        # 插入每张图片
        for i, image_file in enumerate(image_files, 1):
            try:
                image_path = os.path.join(photos_folder, image_file)

                # 生成图片标题
                image_title = self.generate_image_title(image_file)
                title_paragraph = doc.add_paragraph(f"图{i}：{image_title}")
                title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                self.set_paragraph_style(title_paragraph)

                # 插入图片（5英寸宽度，居中）
                picture_paragraph = doc.add_paragraph()
                picture_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = picture_paragraph.runs[0] if picture_paragraph.runs else picture_paragraph.add_run()
                run.add_picture(image_path, width=Inches(5))

                # 添加图片后的空行
                doc.add_paragraph()

            except Exception as e:
                print(f"插入图片 {image_file} 失败: {e}")
                continue

        print(f"✅ 成功插入 {len(image_files)} 张照片")
        return len(image_files)

    def create_widgets(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.configure(style='TFrame')

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        title_frame.columnconfigure(0, weight=1)

        # 主标题
        title_label = ttk.Label(title_frame, text="🏙️ 北京市道路尘负荷数据分析系统",
                               font=("微软雅黑", 20, "bold"),
                               foreground="#2d3748")
        title_label.grid(row=0, column=0, pady=(0, 5))

        # 副标题
        subtitle_label = ttk.Label(title_frame, text="支持丰台区和经开区的数据分析与报告生成",
                                  font=("微软雅黑", 11),
                                  foreground="#718096")
        subtitle_label.grid(row=1, column=0, pady=(0, 10))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 数据文件选择",
                                   padding="12", style='Card.TLabelframe')
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        file_frame.columnconfigure(1, weight=1)

        ttk.Label(file_frame, text="Excel文件:",
                 font=("微软雅黑", 9),
                 foreground="#4a5568").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var,
                                   state="readonly", style='Modern.TEntry',
                                   font=("微软雅黑", 9))
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 8))

        self.browse_button = ttk.Button(file_frame, text="📂 浏览文件",
                                       command=self.browse_file, style='Modern.TButton')
        self.browse_button.grid(row=0, column=2)



        # 区域和街道选择区域
        district_frame = ttk.LabelFrame(main_frame, text="🏙️ 区域和街道选择",
                                       padding="12", style='Card.TLabelframe')
        district_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 12))

        # 区域选择
        ttk.Label(district_frame, text="分析区域:",
                 font=("微软雅黑", 10), foreground="#4a5568").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        self.district_var = tk.StringVar()
        self.district_combo = ttk.Combobox(district_frame, textvariable=self.district_var,
                                         values=list(self.district_streets.keys()),
                                         state="readonly", width=10,
                                         style='Modern.TCombobox',
                                         font=("微软雅黑", 10))
        self.district_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 15))
        self.district_combo.bind("<<ComboboxSelected>>", self.on_district_selected)

        # 街道选择（同一行，紧挨着）
        ttk.Label(district_frame, text="选择街道:",
                 font=("微软雅黑", 10), foreground="#4a5568").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))

        # 多选街道下拉框（调整为更短的宽度）
        self.street_multiselect = MultiSelectCombobox(district_frame, width=18)
        self.street_multiselect.grid(row=0, column=3, sticky=tk.W)

        # 添加说明
        hint_text = "💡 程序会根据导入数据自动选择区域，点击街道下拉框可多选街道（支持复选框）"
        auto_hint = ttk.Label(district_frame, text=hint_text,
                             font=("微软雅黑", 8), foreground="#718096")
        auto_hint.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(12, 0))


        # 报告配置区域
        report_config_frame = ttk.LabelFrame(main_frame, text="📋 报告配置选项",
                                           padding="12", style='Card.TLabelframe')
        report_config_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 12))

        # 报告模板选择
        template_frame = ttk.Frame(report_config_frame)
        template_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        template_frame.columnconfigure(1, weight=1)

        ttk.Label(template_frame, text="报告模板:",
                 font=("微软雅黑", 10), foreground="#4a5568").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))

        self.template_var = tk.StringVar(value="标准报告")
        self.template_combo = ttk.Combobox(template_frame, textvariable=self.template_var,
                                         values=self.template_manager.get_template_names(),
                                         state="readonly", width=15,
                                         style='Modern.TCombobox',
                                         font=("微软雅黑", 9))
        self.template_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 15))
        self.template_combo.bind("<<ComboboxSelected>>", self.on_template_selected)

        # 导出格式选择
        ttk.Label(template_frame, text="导出格式:",
                 font=("微软雅黑", 10), foreground="#4a5568").grid(row=0, column=2, sticky=tk.W, padx=(0, 8))

        self.export_format_var = tk.StringVar(value="Word文档")
        export_formats = ["Word文档", "PDF文档", "Word+PDF"]
        if not PDF_AVAILABLE:
            export_formats = ["Word文档"]

        self.export_format_combo = ttk.Combobox(template_frame, textvariable=self.export_format_var,
                                              values=export_formats,
                                              state="readonly", width=12,
                                              style='Modern.TCombobox',
                                              font=("微软雅黑", 9))
        self.export_format_combo.grid(row=0, column=3, sticky=tk.W)

        # 高级图表选项
        advanced_charts_frame = ttk.Frame(report_config_frame)
        advanced_charts_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(8, 0))

        self.include_advanced_charts_var = tk.BooleanVar(value=ADVANCED_CHARTS_AVAILABLE)
        advanced_charts_checkbox = ttk.Checkbutton(advanced_charts_frame,
                                                 text="📊 包含高级图表（热力图、雷达图、时间序列等）",
                                                 variable=self.include_advanced_charts_var,
                                                 state="normal" if ADVANCED_CHARTS_AVAILABLE else "disabled")
        advanced_charts_checkbox.grid(row=0, column=0, sticky=tk.W)

        # 模板说明
        self.template_description_var = tk.StringVar()
        template_desc_label = ttk.Label(report_config_frame, textvariable=self.template_description_var,
                                       font=("微软雅黑", 8), foreground="#718096")
        template_desc_label.grid(row=2, column=0, sticky=tk.W, pady=(8, 0))

        # 更新模板说明
        self.update_template_description()

        # 数据处理选项区域
        processing_frame = ttk.LabelFrame(main_frame, text="⚙️ 数据处理选项",
                                         padding="12", style='Card.TLabelframe')
        processing_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 12))

        # 重复道路处理选项
        self.remove_duplicates_var = tk.BooleanVar(value=True)  # 默认启用
        duplicate_checkbox = ttk.Checkbutton(processing_frame,
                                           text="🔄 剔除重复道路（同一道路在多个街道时，保留数据量最多的街道）",
                                           variable=self.remove_duplicates_var)
        duplicate_checkbox.grid(row=0, column=0, sticky=tk.W)

        # 异常尘负荷值处理选项
        self.process_zero_values_var = tk.BooleanVar(value=True)  # 默认启用
        zero_values_checkbox = ttk.Checkbutton(processing_frame,
                                             text="🔧 处理异常尘负荷值（去除0.00值，小于0.10的改为0.10）",
                                             variable=self.process_zero_values_var)
        zero_values_checkbox.grid(row=1, column=0, sticky=tk.W, pady=(8, 0))

        # 自动导入照片选项
        self.import_photos_var = tk.BooleanVar(value=False)  # 默认禁用
        photos_checkbox = ttk.Checkbutton(processing_frame,
                                        text="📷 自动导入照片（在报告末尾添加照片）",
                                        variable=self.import_photos_var,
                                        command=self.on_photos_option_changed)
        photos_checkbox.grid(row=2, column=0, sticky=tk.W, pady=(8, 0))

        # 照片文件夹选择
        photos_folder_frame = ttk.Frame(processing_frame)
        photos_folder_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        photos_folder_frame.columnconfigure(1, weight=1)

        ttk.Label(photos_folder_frame, text="照片文件夹:",
                 font=("微软雅黑", 8), foreground="#4a5568").grid(row=0, column=0, sticky=tk.W, padx=(20, 5))

        self.photos_folder_var = tk.StringVar()
        self.photos_folder_entry = ttk.Entry(photos_folder_frame, textvariable=self.photos_folder_var,
                                           state="disabled", style='Modern.TEntry',
                                           font=("微软雅黑", 8))
        self.photos_folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

        self.browse_photos_button = ttk.Button(photos_folder_frame, text="📁 浏览",
                                             command=self.browse_photos_folder,
                                             state="disabled", style='Modern.TButton')
        self.browse_photos_button.grid(row=0, column=2)

        # 始终启用"另存为"对话框（属性供后续代码判断）
        self.use_save_dialog_var = tk.BooleanVar(value=True)

        # 添加说明
        processing_hint = ttk.Label(processing_frame,
                                   text="💡 数据处理选项可根据分析需求灵活调整",
                                   font=("微软雅黑", 8), foreground="#718096")
        processing_hint.grid(row=4, column=0, sticky=tk.W, pady=(8, 0))

        # 处理日志区域（行号后移）
        log_frame = ttk.LabelFrame(main_frame, text="📋 处理日志",
                                  padding="12", style='Card.TLabelframe')
        log_frame.grid(row=7, column=0, sticky=(tk.W, tk.E), pady=(12, 0))
        log_frame.columnconfigure(0, weight=1)

        # 创建带滚动条的文本框
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)

        self.log_text = tk.Text(log_text_frame, height=Config.UI_CONFIG['log_height'], state="disabled",
                               font=("Consolas", 9), bg="#f8f9fa",
                               borderwidth=1, relief='solid',
                               highlightthickness=0,
                               foreground="#2d3748", wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # 添加清空日志按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, pady=(8, 0))

        ttk.Button(log_button_frame, text="🗑️ 清空日志",
                  command=self.clear_log,
                  style='Modern.TButton').pack(side=tk.LEFT)

        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=6, column=0, pady=(12, 0))

        self.refresh_button = ttk.Button(action_frame, text="🔄 刷新数据",
                                        command=self.refresh_data, state="disabled",
                                        style='Modern.TButton')
        self.refresh_button.grid(row=0, column=0, padx=(0, 12))

        self.generate_button = ttk.Button(action_frame, text="📊 生成报告",
                                         command=self.generate_report, state="disabled",
                                         style='Modern.TButton')
        self.generate_button.grid(row=0, column=1)

        # 新增生成图表按钮
        self.chart_button = ttk.Button(action_frame, text="🎨 生成图表",
                                       command=self.generate_charts, state="disabled",
                                       style='Modern.TButton')
        self.chart_button.grid(row=0, column=2, padx=(12, 0))

        # 放大尺寸
        self.chart_button.config(width=14, padding=(6, 4))

        # 生成表格按钮
        self.table_button = ttk.Button(action_frame, text="📑 生成表格",
                                       command=self.generate_tables, state="disabled",
                                       style='Modern.TButton')
        self.table_button.grid(row=0, column=3, padx=(12,0))
        self.table_button.config(width=14, padding=(6,4))

        # 按钮启用同步 generate_button 逻辑：在 load_data 成功时也启用

        # 状态栏
        status_frame = ttk.LabelFrame(main_frame, text="📋 状态信息",
                                     padding="8", style='Card.TLabelframe')
        status_frame.grid(row=8, column=0, sticky=(tk.W, tk.E), pady=(12, 0))
        status_frame.columnconfigure(0, weight=1)

        self.status_var = tk.StringVar(value="请选择Excel文件...")
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                font=("微软雅黑", 9),
                                foreground="#4a5568")
        status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据消息内容添加颜色代码
        if message.startswith("✅"):
            # 成功消息显示为绿色
            color_code = "#008000"  # 绿色
        elif message.startswith("❌") or message.startswith("⚠️"):
            # 错误和警告消息显示为红色
            color_code = "#FF0000"  # 红色
        elif message.startswith("🔄") or message.startswith("📊") or message.startswith("🛣️"):
            # 处理和统计信息显示为蓝色
            color_code = "#0000FF"  # 蓝色
        elif message.startswith("🔍") or message.startswith("🎯"):
            # 搜索和定位信息显示为紫色
            color_code = "#800080"  # 紫色
        else:
            # 默认消息显示为黑色
            color_code = "#000000"  # 黑色

        # 构造带时间戳和颜色的日志条目
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state="normal")

        # 插入带有颜色的文本
        self.log_text.insert(tk.END, log_entry, ("log", ""))

        # 为插入的文本设置颜色标签
        last_line_start = self.log_text.index(f"end-1c linestart")
        last_line_end = self.log_text.index(f"end-1c")
        self.log_text.tag_add("color", last_line_start, last_line_end)
        self.log_text.tag_config("color", foreground=color_code)

        self.log_text.see(tk.END)  # 自动滚动到最新消息
        self.log_text.config(state="disabled")
        self.root.update()  # 立即更新界面

    def center_window(self):
        """将窗口定位在屏幕中央，更可靠的方法"""
        # 解析配置的窗口尺寸
        window_size = Config.UI_CONFIG['window_size']
        width, height = map(int, window_size.split('x'))

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x_pos = (screen_width - width) // 2
        y_pos = (screen_height - height) // 2

        # 设置窗口大小和位置
        self.root.geometry(f"{width}x{height}+{x_pos}+{y_pos}")

        # 显示窗口
        self.root.deiconify()

        # 确保窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

        # 如果日志组件已初始化，则记录日志
        if hasattr(self, 'log_text') and hasattr(self, 'log_message'):
            self.log_message("🖥️ 窗口已居中显示")

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="disabled")
        self.log_message("日志已清空")

    def on_template_selected(self, event=None):
        """模板选择事件处理"""
        self.update_template_description()

    def update_template_description(self):
        """更新模板说明"""
        template_name = self.template_var.get()
        template = self.template_manager.get_template(template_name)
        if template:
            description = f"💡 {template.description}"
            if template.charts:
                chart_count = len(template.charts)
                description += f" | 包含{chart_count}种图表类型"
            self.template_description_var.set(description)
        else:
            self.template_description_var.set("")

    def browse_file(self):
        """浏览并选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择道路尘负荷数据Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            self.selected_file = file_path
            self.file_path_var.set(file_path)
            self.load_data()

    def load_data(self):
        """加载Excel数据"""
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()

            self.log_message(f"开始加载文件: {os.path.basename(self.selected_file)}")

            # 读取Excel文件
            self.df = pd.read_excel(self.selected_file)

            self.log_message(f"✅ 文件加载成功，数据形状: {self.df.shape[0]}行 × {self.df.shape[1]}列")
            self.log_message(f"📋 检测到列名: {', '.join(list(self.df.columns))}")

            # 使用新的数据验证方法
            is_valid, error_messages = self.validate_data_format(self.df)

            if not is_valid:
                self.log_message(f"❌ 数据验证失败:")
                for msg in error_messages:
                    self.log_message(f"  • {msg}")
                messagebox.showerror("数据格式错误",
                                   f"数据验证失败:\n" + "\n".join([f"• {msg}" for msg in error_messages]))
                return

            self.log_message("✅ 数据格式验证通过")

            # 清洗数据
            original_count = len(self.df)
            required_columns = ['道路名称', '所属街道', '尘负荷', '开始时间']
            self.df = self.df.dropna(subset=required_columns)
            cleaned_count = len(self.df)

            if original_count != cleaned_count:
                self.log_message(f"🧹 数据清洗完成，移除了 {original_count - cleaned_count} 条无效记录")

            # 自动检测区域
            data_streets = self.df['所属街道'].unique()
            self.log_message(f"🔍 开始自动检测区域，发现 {len(data_streets)} 个街道")
            detected_district = self.auto_detect_district(data_streets)

            if detected_district:
                # 自动选择检测到的区域
                self.log_message(f"🎯 自动检测到区域: {detected_district}")
                district_index = list(self.district_streets.keys()).index(detected_district)
                self.district_combo.current(district_index)
                self.selected_district = detected_district



                # 刷新街道列表
                self.refresh_streets()

                # 启用按钮
                self.refresh_button.config(state="normal")
                self.generate_button.config(state="normal")
                self.chart_button.config(state="normal")
                self.table_button.config(state="normal")

                self.log_message(f"✅ 数据加载完成！区域: {detected_district}，有效记录: {cleaned_count}条")
                self.status_var.set(f"✅ 数据加载成功！自动选择区域：{detected_district}，共{cleaned_count}条有效记录")
            else:
                # 如果无法自动检测，回退到手动选择
                self.log_message("⚠️ 无法自动检测区域，请手动选择")
                self.district_combo.current(0)  # 默认选择第一个区域
                self.on_district_selected(None)  # 触发区域选择事件

                # 启用按钮
                self.refresh_button.config(state="normal")
                self.generate_button.config(state="normal")
                self.chart_button.config(state="normal")
                self.table_button.config(state="normal")

                self.status_var.set(f"✅ 数据加载成功！未能自动检测区域，请手动选择。共{cleaned_count}条有效记录")

        except Exception as e:
            self.log_message(f"❌ 数据加载失败: {str(e)}")
            messagebox.showerror("加载错误", f"加载Excel文件时出错：\n{str(e)}")
            self.status_var.set("❌ 数据加载失败")



    def on_district_selected(self, event=None):
        """区域选择事件处理"""
        self.selected_district = self.district_var.get()
        if self.selected_district:
            self.log_message(f"🏙️ 用户选择区域: {self.selected_district}")
            self.refresh_streets()
            self.status_var.set(f"已选择{self.selected_district}，请选择街道...")



    def refresh_streets(self):
        """刷新街道下拉框"""
        if self.df is None or not self.selected_district:
            return

        try:
            self.log_message(f"🔄 开始刷新{self.selected_district}的街道列表")

            # 获取数据中的所有街道
            all_streets = self.df['所属街道'].unique()
            district_reference_streets = self.district_streets.get(self.selected_district, [])

            self.log_message(f"📋 数据中发现 {len(all_streets)} 个街道名称")
            self.log_message(f"📚 {self.selected_district}标准街道库包含 {len(district_reference_streets)} 个街道")

            # 筛选出属于选定区域的街道并统计数据量
            self.available_streets = []
            self.street_data_count = {}
            matched_streets = {}  # 存储匹配关系

            # 对数据中的每个街道进行智能匹配
            for data_street in all_streets:
                matched_street = self.smart_street_match(data_street, district_reference_streets)
                if matched_street:
                    # 如果匹配成功，统计数据量
                    count = len(self.df[self.df['所属街道'] == data_street])
                    if count > 0:
                        if matched_street not in self.street_data_count:
                            self.available_streets.append(matched_street)
                            self.street_data_count[matched_street] = 0
                            matched_streets[matched_street] = []

                        self.street_data_count[matched_street] += count
                        matched_streets[matched_street].append(f"{data_street}({count})")

            # 记录匹配结果到日志
            if matched_streets:
                self.log_message(f"✅ 街道匹配完成，找到 {len(matched_streets)} 个有效街道：")
                for standard_street, data_streets in matched_streets.items():
                    if len(data_streets) > 1:
                        self.log_message(f"  📍 {standard_street}: 合并了 {', '.join(data_streets)}")
                    else:
                        self.log_message(f"  📍 {standard_street}: {data_streets[0]}")
            else:
                self.log_message("⚠️ 未找到匹配的街道数据")

            # 更新多选下拉框
            street_options = []
            for street in self.available_streets:
                count = self.street_data_count[street]
                display_text = f"{street} ({count:,}条数据)"
                street_options.append(display_text)

            self.street_multiselect.set_values(street_options)
            self.log_message(f"🏘️ 街道下拉框已更新，共 {len(self.available_streets)} 个可选街道")

            self.status_var.set(f"🏘️ 找到{len(self.available_streets)}个{self.selected_district}街道有数据")

        except Exception as e:
            messagebox.showerror("错误", f"刷新街道列表时出错：{str(e)}")

    def refresh_data(self):
        """刷新数据"""
        if self.df is not None:
            self.refresh_streets()

    def get_selected_streets(self):
        """获取用户选择的街道"""
        selected_items = self.street_multiselect.get_selected()
        # 提取街道名称（去除数据量信息）
        selected_streets = []
        for item in selected_items:
            street_name = item.split(' (')[0]
            selected_streets.append(street_name)
        return selected_streets

    def generate_report(self):
        """生成报告（支持多种格式和模板）"""
        if self.df is None:
            messagebox.showerror("错误", "请先加载数据文件")
            return

        if not self.selected_district:
            messagebox.showerror("错误", "请先选择区域")
            return

        selected_streets = self.get_selected_streets()
        if not selected_streets:
            messagebox.showerror("错误", "请至少选择一个街道")
            return

        try:
            self.status_var.set("正在生成报告...")
            self.root.update()

            # 获取报告配置
            template_name = self.template_var.get()
            export_format = self.export_format_var.get()

            self.log_message("📊 开始生成报告")
            self.log_message(f"📋 使用模板: {template_name}")
            self.log_message(f"📄 导出格式: {export_format}")
            self.log_message(f"🎯 目标区域: {self.selected_district}")
            self.log_message(f"🏘️ 选定街道: {', '.join(selected_streets)}")

            # 设置当前模板
            self.template_manager.set_current_template(template_name)

            # 筛选选定街道的数据 - 使用智能匹配
            district_reference_streets = self.district_streets.get(self.selected_district, [])
            matched_data_streets = []

            # 找出所有匹配选定街道的数据街道名称
            for data_street in self.df['所属街道'].unique():
                matched_street = self.smart_street_match(data_street, district_reference_streets)
                if matched_street in selected_streets:
                    matched_data_streets.append(data_street)

            self.log_message(f"� 数据街道匹配完成，找到 {len(matched_data_streets)} 个匹配的数据街道")
            for data_street in matched_data_streets:
                count = len(self.df[self.df['所属街道'] == data_street])
                self.log_message(f"  📍 {data_street}: {count} 条记录")

            data_df = self.df[self.df['所属街道'].isin(matched_data_streets)]

            if data_df.empty:
                self.log_message("❌ 选定街道没有数据")
                messagebox.showerror("错误", "选定街道没有数据")
                return

            self.log_message(f"📋 初始数据筛选完成，共 {len(data_df)} 条记录")

            # 根据道路匹配表筛选数据
            current_allowed_roads = self.allowed_roads.get(self.selected_district, set())
            if current_allowed_roads:
                # 筛选允许的道路
                filtered_df = data_df[data_df['道路名称'].isin(current_allowed_roads)].copy()
                self.log_message(f"🛣️ 道路筛选: {len(data_df)} → {len(filtered_df)} 条记录")
                self.log_message(f"📊 使用道路匹配表，允许 {len(current_allowed_roads)} 条道路")
            else:
                # 如果没有道路匹配表，使用所有数据
                filtered_df = data_df.copy()
                self.log_message("⚠️ 未启用道路筛选，使用所有数据")

            # 标准化街道名称：将数据中的街道名称映射为用户选择的标准街道名称
            print("正在标准化街道名称...")
            district_reference_streets = self.district_streets.get(self.selected_district, [])

            # 创建街道名称映射
            street_mapping = {}
            for data_street in filtered_df['所属街道'].unique():
                matched_street = self.smart_street_match(data_street, district_reference_streets)
                if matched_street and matched_street in selected_streets:
                    street_mapping[data_street] = matched_street
                    if data_street != matched_street:
                        print(f"🔄 报告中街道名称标准化: '{data_street}' → '{matched_street}'")

            # 应用街道名称映射
            filtered_df['标准街道名称'] = filtered_df['所属街道'].map(street_mapping)

            # 只保留成功映射的数据
            filtered_df = filtered_df[filtered_df['标准街道名称'].notna()].copy()

            if filtered_df.empty:
                messagebox.showerror("错误", "没有找到匹配的街道数据")
                return

            # 根据用户选择决定是否处理异常尘负荷值
            abnormal_stats = {"removed_zero": 0, "corrected_small": 0}
            if self.process_zero_values_var.get():
                filtered_df, abnormal_stats = self.process_abnormal_dust_values(filtered_df)
                if filtered_df.empty:
                    messagebox.showerror("错误", "处理异常值后没有剩余数据")
                    return


            # 根据用户选择决定是否处理重复道路
            if self.remove_duplicates_var.get():
                # 使用标准街道名称进行重复道路处理
                filtered_df_for_dedup = filtered_df.copy()
                filtered_df_for_dedup['所属街道'] = filtered_df_for_dedup['标准街道名称']
                filtered_df = self.remove_duplicate_roads(filtered_df_for_dedup)

            # 数据透视处理 - 按标准街道名称和道路分组
            pivot_data = filtered_df.groupby(['标准街道名称', '道路名称']).agg({
                '尘负荷': 'mean',  # 计算尘负荷列的平均值
                '开始时间': 'first'  # 取第一个时间
            }).reset_index()

            # 根据尘负荷平均值重新计算等级
            pivot_data['等级'] = pivot_data['尘负荷'].apply(self.get_dust_level)
            self.log_message(f"📊 透视处理完成，按道路平均值计算等级")
            level_counts = pivot_data['等级'].value_counts()
            self.log_message(f"📈 等级分布：优({level_counts.get('优', 0)}条), 良({level_counts.get('良', 0)}条), 中({level_counts.get('中', 0)}条), 差({level_counts.get('差', 0)}条)")

            # 重命名列以保持后续代码兼容性
            pivot_data = pivot_data.rename(columns={'标准街道名称': '所属街道'})

            # 按尘负荷平均值排序（从小到大）
            sorted_data = pivot_data.sort_values('尘负荷', ascending=True)
            self.log_message(f"🔢 数据排序完成，按尘负荷从小到大排序")

            # 创建Word文档
            self.log_message(f"📄 开始创建Word文档，使用模板: {template_name}")
            doc = Document()

            # 设置文档字体 - 中文仿宋_GB2312，英文数字Times New Roman
            normal_style = doc.styles['Normal']
            normal_style.font.name = 'Times New Roman'  # 英文数字字体
            normal_style._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')  # 中文字体
            normal_style.font.size = Pt(12)

            # 添加标题
            current_date = datetime.now().strftime("%Y.%m.%d")
            title = f"北京市{self.selected_district}尘负荷监测结果报告（{current_date}）"
            title_paragraph = doc.add_heading(title, level=1)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 设置标题样式 - 三号字体，黑色
            title_run = title_paragraph.runs[0]
            title_run.font.name = 'Times New Roman'  # 英文数字字体
            title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')  # 中文字体
            title_run.font.size = Pt(16)  # 三号字体
            title_run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色

            # 计算统计数据
            total_roads = len(sorted_data)
            level_counts = sorted_data['等级'].value_counts()

            # 获取监测时间范围
            time_range = self.get_monitoring_time_range(sorted_data)

            # 获取当前模板
            current_template = self.template_manager.get_template(template_name)
            if not current_template:
                self.log_message(f"⚠️ 未找到模板 {template_name}，使用标准报告模板")
                current_template = self.template_manager.get_template("标准报告")

            # 根据模板生成内容
            self.log_message(f"📋 根据模板 '{current_template.name}' 生成报告内容")
            self._generate_word_content_by_template(doc, current_template, sorted_data, total_roads, level_counts, time_range)

            # 添加照片（如果启用）
            photos_count = self.add_photos_to_document(doc)

            # 准备报告信息
            street_list = "、".join(selected_streets)
            total_roads = len(sorted_data)
            original_records = len(data_df)

            # 数据处理信息
            duplicate_status = "已处理" if self.remove_duplicates_var.get() else "已跳过"
            abnormal_status = "已处理" if self.process_zero_values_var.get() else "已跳过"

            processing_details = []
            if self.process_zero_values_var.get() and (abnormal_stats["removed_zero"] > 0 or abnormal_stats["corrected_small"] > 0):
                if abnormal_stats["removed_zero"] > 0:
                    processing_details.append(f"去除0.00值{abnormal_stats['removed_zero']}条")
                if abnormal_stats["corrected_small"] > 0:
                    processing_details.append(f"修正小值{abnormal_stats['corrected_small']}条")

            if current_allowed_roads:
                filter_info = f"\n🔍 数据处理: {original_records:,} → {len(filtered_df):,} 条记录"
                filter_info += f"（道路筛选，异常值{abnormal_status}，重复道路{duplicate_status}）"
            else:
                filter_info = f"\n🔍 数据处理: {original_records:,} → {len(filtered_df):,} 条记录"
                filter_info += f"（异常值{abnormal_status}，重复道路{duplicate_status}）"

            if processing_details:
                filter_info += f"\n📊 异常值处理: {', '.join(processing_details)}"

            # 照片导入信息
            photos_info = ""
            if self.import_photos_var.get():
                if photos_count > 0:
                    photos_info = f"\n📷 照片导入: 成功导入 {photos_count} 张照片"
                else:
                    photos_info = f"\n📷 照片导入: 未找到照片文件"

            # 根据导出格式处理
            if export_format == "Word文档":
                success = self._save_word_report(doc, selected_streets, sorted_data, filter_info, abnormal_stats, current_allowed_roads, photos_count, total_roads, original_records, street_list, photos_info)
            elif export_format == "PDF文档":
                success = self._save_pdf_report(sorted_data, selected_streets, template_name)
            elif export_format == "Word+PDF":
                # 先保存Word
                word_success = self._save_word_report(doc, selected_streets, sorted_data, filter_info, abnormal_stats, current_allowed_roads, photos_count, total_roads, original_records, street_list, photos_info)
                # 再保存PDF
                pdf_success = self._save_pdf_report(sorted_data, selected_streets, template_name)
                success = word_success and pdf_success
            else:
                success = self._save_word_report(doc, selected_streets, sorted_data, filter_info, abnormal_stats, current_allowed_roads, photos_count, total_roads, original_records, street_list, photos_info)

            if not success:
                self.status_var.set("❌ 报告保存失败")
                return

            # 显示成功消息
            messagebox.showinfo("生成成功", f"📄 报告生成成功！\n\n🏙️ 分析区域: {self.selected_district}\n🏘️ 包含街道: {street_list}\n🛣️ 道路数量: {total_roads:,} 条道路\n📊 原始数据: {original_records:,} 条记录")
            self.status_var.set(f"✅ 报告生成成功！包含{total_roads}条道路")

        except Exception as e:
            messagebox.showerror("生成失败", f"生成报告时出错：\n{str(e)}")
            self.status_var.set("❌ 报告生成失败")

    def _generate_word_content_by_template(self, doc, template, sorted_data, total_roads, level_counts, time_range):
        """根据模板生成Word文档内容"""
        try:
            self.log_message(f"📝 使用模板 '{template.name}' 生成内容，包含 {len(template.sections)} 个章节")

            # 根据模板的章节配置生成内容
            for section in template.sections:
                section_type = section.get('type', 'heading')
                section_title = section.get('title', '')
                content_type = section.get('content_type', 'text')

                self.log_message(f"📄 生成章节: {section_title} ({content_type})")

                if section_type == 'heading' and section_title:
                    # 添加章节标题
                    heading = doc.add_heading(section_title, level=2)
                    self.set_heading_style(heading)

                    # 根据内容类型生成具体内容
                    if content_type == 'summary':
                        self._add_summary_section(doc, total_roads, level_counts, time_range)
                    elif content_type == 'road_table':
                        self._add_road_table_section(doc, sorted_data)
                    elif content_type == 'street_table':
                        self._add_street_table_section(doc, sorted_data)
                    elif content_type == 'poor_roads':
                        self._add_poor_roads_section(doc, sorted_data, total_roads)
                    elif content_type == 'poor_roads_detailed':
                        self._add_poor_roads_detailed_section(doc, sorted_data, total_roads)
                    elif content_type == 'data_quality':
                        self._add_data_quality_section(doc, sorted_data)
                    elif content_type == 'time_analysis':
                        self._add_time_analysis_section(doc, sorted_data)
                    elif content_type == 'charts':
                        self._add_charts_section(doc, template, sorted_data)


        except Exception as e:
            self.log_message(f"❌ 模板内容生成失败: {e}")
            # 如果模板生成失败，回退到基础内容
            self._add_basic_content(doc, sorted_data, total_roads, level_counts, time_range)

    def _add_summary_section(self, doc, total_roads, level_counts, time_range):
        """添加监测结果概述"""
        result_content = f"本次共监测道路{total_roads}条，道路监测时间为{time_range}。"

        # 添加各等级统计
        for level in ['优', '良', '中', '差']:
            count = level_counts.get(level, 0)
            percentage = (count / total_roads * 100) if total_roads > 0 else 0
            result_content += f"所监测道路中{level}级道路{count}条，占比{percentage:.1f}%；"

        result_content = result_content.rstrip('；') + "。"
        result_paragraph = doc.add_paragraph(result_content)
        self.set_paragraph_style(result_paragraph)

    def _add_road_table_section(self, doc, sorted_data):
        """添加监测道路汇总表"""
        # 创建表格
        table = doc.add_table(rows=1, cols=6)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 设置表头
        headers = ['序号', '道路名称', '街道名称', '尘负荷', '时间', '等级']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = paragraph.runs[0]
            run.font.bold = True
            run.font.size = Pt(11)
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            run.font.color.rgb = RGBColor(0, 0, 0)
            self.set_cell_background_color(cell, "D9D9D9")
            self.set_cell_vertical_alignment_and_spacing(cell)

        # 添加数据行
        for idx, (_, row) in enumerate(sorted_data.iterrows(), 1):
            row_cells = table.add_row().cells

            # 序号
            row_cells[0].text = str(idx)
            row_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[0])
            self.set_cell_vertical_alignment_and_spacing(row_cells[0])

            # 道路名称
            row_cells[1].text = str(row['道路名称'])
            row_cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[1])
            self.set_cell_vertical_alignment_and_spacing(row_cells[1])

            # 街道名称
            row_cells[2].text = str(row['所属街道'])
            row_cells[2].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[2])
            self.set_cell_vertical_alignment_and_spacing(row_cells[2])

            # 尘负荷
            dust_load = row['尘负荷']
            row_cells[3].text = f"{dust_load:.2f}"
            row_cells[3].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[3])
            self.set_cell_vertical_alignment_and_spacing(row_cells[3])

            # 时间处理
            time_str = self._format_time_string(row.get('开始时间', ''))
            row_cells[4].text = time_str
            row_cells[4].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[4])
            self.set_cell_vertical_alignment_and_spacing(row_cells[4])

            # 等级
            row_cells[5].text = str(row['等级'])
            row_cells[5].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[5])
            self.set_cell_vertical_alignment_and_spacing(row_cells[5])

    def _add_street_table_section(self, doc, sorted_data):
        """添加街道监测统计表"""
        doc.add_paragraph()  # 添加空行

        # 创建街道统计表
        street_stats = sorted_data.groupby('所属街道').agg({
            '道路名称': 'count',
            '尘负荷': 'mean'
        }).reset_index()
        street_stats.columns = ['街道名称', '道路数量（条）', '平均尘负荷']
        street_stats['等级'] = street_stats['平均尘负荷'].apply(self.get_dust_level)
        street_stats = street_stats.sort_values('平均尘负荷', ascending=True)

        # 创建表格
        street_table = doc.add_table(rows=1, cols=4)
        street_table.style = 'Table Grid'
        street_table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 设置表头
        street_headers = ['街道名称', '道路数量（条）', '平均尘负荷', '等级']
        street_header_row = street_table.rows[0]
        for i, header in enumerate(street_headers):
            cell = street_header_row.cells[i]
            cell.text = header
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = paragraph.runs[0]
            run.font.bold = True
            run.font.size = Pt(11)
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            run.font.color.rgb = RGBColor(0, 0, 0)
            self.set_cell_background_color(cell, "D9D9D9")
            self.set_cell_vertical_alignment_and_spacing(cell)

        # 添加数据行
        for _, street_row in street_stats.iterrows():
            row_cells = street_table.add_row().cells

            row_cells[0].text = str(street_row['街道名称'])
            row_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[0])
            self.set_cell_vertical_alignment_and_spacing(row_cells[0])

            row_cells[1].text = str(street_row['道路数量（条）'])
            row_cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[1])
            self.set_cell_vertical_alignment_and_spacing(row_cells[1])

            row_cells[2].text = f"{street_row['平均尘负荷']:.2f}"
            row_cells[2].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[2])
            self.set_cell_vertical_alignment_and_spacing(row_cells[2])

            row_cells[3].text = str(street_row['等级'])
            row_cells[3].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            self.set_cell_font(row_cells[3])
            self.set_cell_vertical_alignment_and_spacing(row_cells[3])

    def _add_poor_roads_section(self, doc, sorted_data, total_roads):
        """添加差等级道路情况（基础版）"""
        poor_roads = sorted_data[sorted_data['等级'] == '差']
        if len(poor_roads) > 0:
            doc.add_paragraph()  # 添加空行

            poor_count = len(poor_roads)
            poor_percentage = (poor_count / total_roads * 100) if total_roads > 0 else 0
            poor_reasons = self.get_random_reasons_and_measures()

            poor_content = f"差等级道路为{poor_count}条，占比{poor_percentage:.1f}%。主要原因：{poor_reasons}"
            poor_paragraph = doc.add_paragraph(poor_content)
            self.set_paragraph_style(poor_paragraph)

            # 如果差等级道路较少，列出具体道路
            if poor_count <= 10:
                poor_roads_list = "、".join(poor_roads['道路名称'].tolist())
                detail_content = f"具体道路包括：{poor_roads_list}。"
                detail_paragraph = doc.add_paragraph(detail_content)
                self.set_paragraph_style(detail_paragraph)
        else:
            no_poor_content = "本次监测中未发现差等级道路，整体道路清洁状况良好。"
            no_poor_paragraph = doc.add_paragraph(no_poor_content)
            self.set_paragraph_style(no_poor_paragraph)

    def _add_poor_roads_detailed_section(self, doc, sorted_data, total_roads):
        """添加差等级道路深度分析"""
        poor_roads = sorted_data[sorted_data['等级'] == '差']
        if len(poor_roads) > 0:
            poor_count = len(poor_roads)
            poor_percentage = (poor_count / total_roads * 100) if total_roads > 0 else 0

            # 基本情况
            basic_info = f"差等级道路共{poor_count}条，占总监测道路的{poor_percentage:.1f}%。通过对差等级道路的深入分析，发现主要问题集中在以下几个方面："
            basic_paragraph = doc.add_paragraph(basic_info)
            self.set_paragraph_style(basic_paragraph)

            # 问题分析
            problem1 = "1. 道路清扫频次不足：部分道路清扫间隔时间过长，导致尘土积累；"
            problem1_paragraph = doc.add_paragraph(problem1)
            self.set_paragraph_style(problem1_paragraph)

            problem2 = "2. 施工影响：周边建设工程产生的扬尘对道路清洁度造成影响；"
            problem2_paragraph = doc.add_paragraph(problem2)
            self.set_paragraph_style(problem2_paragraph)

            problem3 = "3. 交通流量大：车辆通行频繁的路段更容易积累尘土；"
            problem3_paragraph = doc.add_paragraph(problem3)
            self.set_paragraph_style(problem3_paragraph)

            problem4 = "4. 天气因素：干燥天气和大风天气加剧了道路扬尘问题。"
            problem4_paragraph = doc.add_paragraph(problem4)
            self.set_paragraph_style(problem4_paragraph)

            # 改进措施
            measures_intro = "建议采取以下措施："
            measures_intro_paragraph = doc.add_paragraph(measures_intro)
            self.set_paragraph_style(measures_intro_paragraph)

            measure1 = "- 增加清扫频次，特别是在交通高峰期后；"
            measure1_paragraph = doc.add_paragraph(measure1)
            self.set_paragraph_style(measure1_paragraph)

            measure2 = "- 加强施工现场管理，减少扬尘产生；"
            measure2_paragraph = doc.add_paragraph(measure2)
            self.set_paragraph_style(measure2_paragraph)

            measure3 = "- 在干燥天气适当增加洒水频次；"
            measure3_paragraph = doc.add_paragraph(measure3)
            self.set_paragraph_style(measure3_paragraph)

            measure4 = "- 对重点路段实施精细化管理。"
            measure4_paragraph = doc.add_paragraph(measure4)
            self.set_paragraph_style(measure4_paragraph)
        else:
            no_poor_content = "本次监测中未发现差等级道路，道路清洁管理效果显著，建议继续保持现有管理水平。"
            no_poor_paragraph = doc.add_paragraph(no_poor_content)
            self.set_paragraph_style(no_poor_paragraph)

    def _add_data_quality_section(self, doc, sorted_data):
        """添加数据质量评估"""
        total_records = len(sorted_data)
        unique_roads = sorted_data['道路名称'].nunique()
        unique_streets = sorted_data['所属街道'].nunique()

        # 数据完整性分析
        time_coverage = "完整" if '开始时间' in sorted_data.columns and sorted_data['开始时间'].notna().sum() > 0 else "部分缺失"
        dust_load_range = f"{sorted_data['尘负荷'].min():.2f} - {sorted_data['尘负荷'].max():.2f}"

        # 评估结果介绍
        intro_content = "本次数据质量评估结果如下："
        intro_paragraph = doc.add_paragraph(intro_content)
        self.set_paragraph_style(intro_paragraph)

        # 数据覆盖范围
        coverage_content = f"数据覆盖范围：监测记录总数：{total_records}条；涉及道路数量：{unique_roads}条；涉及街道数量：{unique_streets}个；时间信息完整性：{time_coverage}；尘负荷数值范围：{dust_load_range}克/平方米。"
        coverage_paragraph = doc.add_paragraph(coverage_content)
        self.set_paragraph_style(coverage_paragraph)

        # 质量评价
        evaluation_content = "数据质量评价：数据结构完整，数值范围合理，能够支撑有效的分析和决策。"
        evaluation_paragraph = doc.add_paragraph(evaluation_content)
        self.set_paragraph_style(evaluation_paragraph)

    def _add_time_analysis_section(self, doc, sorted_data):
        """添加时间趋势分析"""
        if '开始时间' in sorted_data.columns:
            # 时间分析结果
            analysis_intro = "基于监测时间数据的趋势分析显示："
            analysis_paragraph = doc.add_paragraph(analysis_intro)
            self.set_paragraph_style(analysis_paragraph)

            feature1 = "1. 时间分布特征：监测数据覆盖了不同时间段，为分析提供了良好的时间维度支撑；"
            feature1_paragraph = doc.add_paragraph(feature1)
            self.set_paragraph_style(feature1_paragraph)

            feature2 = "2. 变化趋势：通过时间序列分析，可以识别道路清洁度的变化规律；"
            feature2_paragraph = doc.add_paragraph(feature2)
            self.set_paragraph_style(feature2_paragraph)

            feature3 = "3. 周期性特征：部分道路显示出明显的时间周期性变化特征；"
            feature3_paragraph = doc.add_paragraph(feature3)
            self.set_paragraph_style(feature3_paragraph)

            feature4 = "4. 异常时段识别：通过时间分析识别出需要重点关注的时间段。"
            feature4_paragraph = doc.add_paragraph(feature4)
            self.set_paragraph_style(feature4_paragraph)

            # 建议
            suggestions_intro = "建议："
            suggestions_paragraph = doc.add_paragraph(suggestions_intro)
            self.set_paragraph_style(suggestions_paragraph)

            suggestion1 = "- 建立定期监测机制，持续跟踪道路清洁度变化；"
            suggestion1_paragraph = doc.add_paragraph(suggestion1)
            self.set_paragraph_style(suggestion1_paragraph)

            suggestion2 = "- 针对识别出的问题时段，加强清洁管理措施；"
            suggestion2_paragraph = doc.add_paragraph(suggestion2)
            self.set_paragraph_style(suggestion2_paragraph)

            suggestion3 = "- 结合天气、交通等外部因素，优化清洁作业时间安排。"
            suggestion3_paragraph = doc.add_paragraph(suggestion3)
            self.set_paragraph_style(suggestion3_paragraph)
        else:
            time_content = "本次数据中时间信息不完整，建议在后续监测中完善时间记录，以便进行更深入的趋势分析。"
            time_paragraph = doc.add_paragraph(time_content)
            self.set_paragraph_style(time_paragraph)

    def _add_charts_section(self, doc, template, sorted_data=None):
        """添加图表章节（包含实际图表）"""
        chart_count = len(template.charts)
        chart_types = [chart['type'] for chart in template.charts]

        # 添加图表说明
        charts_intro = f"本报告包含{chart_count}种类型的数据可视化图表："
        charts_paragraph = doc.add_paragraph(charts_intro)
        self.set_paragraph_style(charts_paragraph)

        chart_descriptions = {
            'bar': '柱状图：直观展示各街道平均尘负荷对比',
            'pie': '饼图：清晰显示道路等级分布比例',
            'scatter': '散点图：分析差等级道路的分布特征',
            'heatmap': '热力图：展示街道-时间维度的尘负荷分布',
            'radar': '雷达图：多维度评估各街道综合表现',
            'timeline': '时间序列图：显示尘负荷随时间的变化趋势',
            'box': '箱线图：显示污染分布的统计特征',
            'violin': '小提琴图：展示污染密度分布',
            'correlation': '相关性矩阵：显示变量间的关联强度',
            'trend': '趋势分解图：分析时间序列的趋势成分'
        }

        for i, chart_type in enumerate(chart_types, 1):
            description = chart_descriptions.get(chart_type, f'{chart_type}图表：提供专业的数据分析视角')
            chart_item = f"{i}. {description}"
            chart_paragraph = doc.add_paragraph(chart_item)
            self.set_paragraph_style(chart_paragraph)

        charts_footer = "以下为具体的数据可视化图表："
        footer_paragraph = doc.add_paragraph(charts_footer)
        self.set_paragraph_style(footer_paragraph)

        # 生成并嵌入实际图表
        if sorted_data is not None and plt is not None:
            self._generate_and_embed_charts(doc, template, sorted_data)
        else:
            # 如果无法生成图表，添加说明
            fallback_content = "注：图表生成需要matplotlib库支持，请安装相关依赖后重新生成报告。"
            fallback_paragraph = doc.add_paragraph(fallback_content)
            self.set_paragraph_style(fallback_paragraph)

    def _generate_and_embed_charts(self, doc, template, sorted_data):
        """生成并嵌入图表到Word文档"""
        try:
            self.log_message("📊 开始生成报告图表...")

            # 创建临时目录存储图表
            import tempfile
            temp_dir = tempfile.mkdtemp()
            chart_files = []

            for i, chart_config in enumerate(template.charts):
                chart_type = chart_config['type']
                chart_title = chart_config['title']

                try:
                    self.log_message(f"📈 生成图表: {chart_title}")

                    # 根据图表类型生成对应图表
                    fig = None
                    if chart_type == 'bar':
                        fig = self._create_bar_chart(sorted_data, chart_title)
                    elif chart_type == 'pie':
                        fig = self._create_pie_chart(sorted_data, chart_title)
                    elif chart_type == 'scatter':
                        fig = self._create_scatter_chart(sorted_data, chart_title)
                    elif chart_type == 'box':
                        fig = self._create_box_chart(sorted_data, chart_title)
                    elif chart_type == 'violin':
                        fig = self._create_violin_chart(sorted_data, chart_title)
                    elif chart_type == 'heatmap' and ADVANCED_CHARTS_AVAILABLE:
                        fig = self._create_heatmap_chart(sorted_data, chart_title)
                    elif chart_type in ['radar', 'timeline', 'correlation', 'trend']:
                        # 高级图表暂时用占位符
                        fig = self._create_placeholder_chart(chart_title, f"高级{chart_type}图表")

                    if fig is not None:
                        # 保存图表为临时文件
                        chart_file = os.path.join(temp_dir, f"chart_{i+1}_{chart_type}.png")
                        fig.savefig(chart_file, dpi=150, bbox_inches='tight',
                                  facecolor='white', edgecolor='none')
                        chart_files.append((chart_file, chart_title))
                        plt.close(fig)  # 关闭图形以释放内存

                except Exception as e:
                    self.log_message(f"❌ 图表生成失败 {chart_title}: {e}")
                    continue

            # 将图表嵌入到Word文档中
            for chart_file, chart_title in chart_files:
                try:
                    # 添加图表标题
                    chart_title_paragraph = doc.add_paragraph()
                    chart_title_run = chart_title_paragraph.add_run(f"\n图表：{chart_title}")
                    chart_title_run.font.bold = True
                    chart_title_run.font.size = Pt(12)
                    chart_title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # 添加图表图片
                    chart_paragraph = doc.add_paragraph()
                    chart_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    run = chart_paragraph.runs[0] if chart_paragraph.runs else chart_paragraph.add_run()

                    # 插入图片，设置合适的尺寸
                    run.add_picture(chart_file, width=Inches(6))

                    self.log_message(f"✅ 图表已嵌入: {chart_title}")

                except Exception as e:
                    self.log_message(f"❌ 图表嵌入失败 {chart_title}: {e}")
                    continue

            # 清理临时文件
            import shutil
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

            self.log_message(f"✅ 报告图表生成完成，共嵌入 {len(chart_files)} 个图表")

        except Exception as e:
            self.log_message(f"❌ 图表生成过程失败: {e}")
            error_content = f"图表生成过程中出现错误：{str(e)}"
            error_paragraph = doc.add_paragraph(error_content)
            self.set_paragraph_style(error_paragraph)

    def _create_bar_chart(self, sorted_data, title):
        """创建柱状图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            # 计算街道平均尘负荷
            street_stats = sorted_data.groupby('所属街道')['尘负荷'].mean().sort_values(ascending=True)

            # 使用与生成图表按钮一致的颜色映射
            def _get_color(val):
                th = Config.DUST_LEVEL_THRESHOLDS
                if val <= th['优']:
                    return '#1919FF'  # 蓝色 - 优级
                elif val <= th['良']:
                    return '#FFFF00'  # 黄色 - 良级
                elif val <= th['中']:
                    return '#FF7E00'  # 橙色 - 中级
                else:
                    return '#FF0000'  # 红色 - 差级

            # 创建柱状图，使用标准颜色
            colors = [_get_color(v) for v in street_stats.values]
            ax.barh(range(len(street_stats)), street_stats.values, color=colors)

            # 设置标签
            ax.set_yticks(range(len(street_stats)))
            ax.set_yticklabels(street_stats.index)
            ax.set_xlabel('平均尘负荷 (克/平方米)')
            ax.set_title(title, fontsize=14, fontweight='bold')

            # 添加数值标签
            for i, v in enumerate(street_stats.values):
                ax.text(v + 0.01, i, f'{v:.3f}', va='center', fontsize=10)

            # 添加等级线，使用标准颜色
            ax.axvline(x=Config.DUST_LEVEL_THRESHOLDS['优'], color='#1919FF', linestyle='--', alpha=0.7, label='优级标准')
            ax.axvline(x=Config.DUST_LEVEL_THRESHOLDS['良'], color='#FFFF00', linestyle='--', alpha=0.7, label='良级标准')
            ax.axvline(x=Config.DUST_LEVEL_THRESHOLDS['中'], color='#FF7E00', linestyle='--', alpha=0.7, label='中级标准')
            ax.legend()

            plt.tight_layout()
            return fig

        except Exception as e:
            self.log_message(f"❌ 创建柱状图失败: {e}")
            return None

    def _create_pie_chart(self, sorted_data, title):
        """创建饼图"""
        try:
            fig, ax = plt.subplots(figsize=(8, 8))

            # 计算等级分布
            if '等级' in sorted_data.columns:
                level_counts = sorted_data['等级'].value_counts()
            else:
                # 如果没有等级列，根据尘负荷计算
                levels = sorted_data['尘负荷'].apply(self.get_dust_level)
                level_counts = levels.value_counts()

            # 使用与生成图表按钮一致的颜色配置
            standard_colors = {'优': '#1919FF', '良': '#FFFF00', '中': '#FF7E00', '差': '#FF0000'}

            # 确保按照标准顺序排列
            level_order = ['优', '良', '中', '差']
            ordered_counts = []
            ordered_colors = []
            ordered_labels = []

            for level in level_order:
                if level in level_counts.index:
                    ordered_counts.append(level_counts[level])
                    ordered_colors.append(standard_colors[level])
                    ordered_labels.append(level)

            # 创建饼图
            wedges, _, autotexts = ax.pie(ordered_counts, labels=ordered_labels,
                                        autopct='%1.1f%%', colors=ordered_colors, startangle=90)

            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(12)

            ax.set_title(title, fontsize=14, fontweight='bold')

            # 添加图例
            ax.legend(wedges, [f'{level}: {count}条' for level, count in zip(ordered_labels, ordered_counts)],
                     title="道路等级", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

            plt.tight_layout()
            return fig

        except Exception as e:
            self.log_message(f"❌ 创建饼图失败: {e}")
            return None

    def _create_scatter_chart(self, sorted_data, title):
        """创建散点图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            # 筛选差等级道路
            if '等级' in sorted_data.columns:
                poor_roads = sorted_data[sorted_data['等级'] == '差']
            else:
                poor_roads = sorted_data[sorted_data['尘负荷'] > 1.0]

            if len(poor_roads) > 0:
                # 为避免重叠，添加随机偏移
                import numpy as np
                x_values = range(len(poor_roads))
                y_values = poor_roads['尘负荷'].values

                # 添加小的随机偏移
                x_jitter = np.random.normal(0, 0.1, len(x_values))

                scatter = ax.scatter([x + jitter for x, jitter in zip(x_values, x_jitter)],
                                  y_values, c=y_values, cmap='Reds', s=100, alpha=0.7)

                # 设置标签
                ax.set_xticks(range(len(poor_roads)))
                ax.set_xticklabels([f"{row['道路名称']}\n({row['所属街道']})"
                                  for _, row in poor_roads.iterrows()], rotation=45, ha='right')
                ax.set_ylabel('尘负荷 (克/平方米)')
                ax.set_title(title, fontsize=14, fontweight='bold')

                # 添加颜色条
                cbar = plt.colorbar(scatter)
                cbar.set_label('尘负荷 (克/平方米)')

            else:
                ax.text(0.5, 0.5, '未发现差等级道路', ha='center', va='center',
                       transform=ax.transAxes, fontsize=16)
                ax.set_title(title, fontsize=14, fontweight='bold')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"创建散点图失败: {e}")
            return None

    def _create_box_chart(self, sorted_data, title):
        """创建箱线图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            # 按街道分组数据
            street_data = []
            street_names = []

            for street in sorted_data['所属街道'].unique():
                street_dust = sorted_data[sorted_data['所属街道'] == street]['尘负荷']
                street_data.append(street_dust)
                street_names.append(street)

            # 创建箱线图
            box_plot = ax.boxplot(street_data, labels=street_names, patch_artist=True)

            # 设置颜色
            colors = ['#45b7d1', '#4ecdc4', '#ffa726', '#ff6b6b', '#9b59b6', '#2ecc71']
            for patch, color in zip(box_plot['boxes'], colors[:len(box_plot['boxes'])]):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            ax.set_ylabel('尘负荷 (克/平方米)')
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.tick_params(axis='x', rotation=45)

            # 添加等级线
            ax.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='良级标准')
            ax.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='中级标准')
            ax.legend()

            plt.tight_layout()
            return fig

        except Exception as e:
            self.log_message(f"❌ 创建箱线图失败: {e}")
            return None

    def _create_violin_chart(self, sorted_data, title):
        """创建小提琴图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            # 按街道分组数据
            street_data = []
            street_names = []

            for street in sorted_data['所属街道'].unique():
                street_dust = sorted_data[sorted_data['所属街道'] == street]['尘负荷']
                if len(street_dust) > 1:  # 小提琴图需要多个数据点
                    street_data.append(street_dust)
                    street_names.append(street)

            if len(street_data) > 0:
                # 创建小提琴图
                violin_parts = ax.violinplot(street_data, positions=range(1, len(street_data) + 1))

                # 设置颜色
                colors = ['#45b7d1', '#4ecdc4', '#ffa726', '#ff6b6b', '#9b59b6', '#2ecc71']
                for pc, color in zip(violin_parts['bodies'], colors[:len(violin_parts['bodies'])]):
                    pc.set_facecolor(color)
                    pc.set_alpha(0.7)

                ax.set_xticks(range(1, len(street_names) + 1))
                ax.set_xticklabels(street_names, rotation=45)
                ax.set_ylabel('尘负荷 (克/平方米)')
                ax.set_title(title, fontsize=14, fontweight='bold')

                # 添加等级线
                ax.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='良级标准')
                ax.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='中级标准')
                ax.legend()
            else:
                ax.text(0.5, 0.5, '数据不足，无法生成小提琴图', ha='center', va='center',
                       transform=ax.transAxes, fontsize=16)
                ax.set_title(title, fontsize=14, fontweight='bold')

            plt.tight_layout()
            return fig

        except Exception as e:
            self.log_message(f"❌ 创建小提琴图失败: {e}")
            return None

    def _create_heatmap_chart(self, sorted_data, title):
        """创建热力图"""
        try:
            if not ADVANCED_CHARTS_AVAILABLE:
                return self._create_placeholder_chart(title, "需要seaborn库支持")

            fig, ax = plt.subplots(figsize=(12, 8))

            # 如果有时间数据，创建时间-街道热力图
            if '开始时间' in sorted_data.columns:
                data_copy = sorted_data.copy()
                data_copy['小时'] = pd.to_datetime(data_copy['开始时间']).dt.hour

                # 创建透视表
                heatmap_data = data_copy.groupby(['所属街道', '小时'])['尘负荷'].mean().unstack(fill_value=0)

                # 创建热力图
                sns.heatmap(heatmap_data, annot=True, fmt='.2f', cmap='YlOrRd', ax=ax)
                ax.set_title(title, fontsize=14, fontweight='bold')
                ax.set_xlabel('小时')
                ax.set_ylabel('街道')
            else:
                # 如果没有时间数据，创建街道相关性热力图
                street_pivot = sorted_data.pivot_table(values='尘负荷',
                                                     index='所属街道',
                                                     columns='道路名称',
                                                     aggfunc='mean').fillna(0)

                if len(street_pivot) > 1:
                    correlation_matrix = street_pivot.corr()
                    sns.heatmap(correlation_matrix, annot=True, fmt='.2f', cmap='coolwarm', ax=ax)
                    ax.set_title(title, fontsize=14, fontweight='bold')
                else:
                    ax.text(0.5, 0.5, '数据不足，无法生成热力图', ha='center', va='center',
                           transform=ax.transAxes, fontsize=16)
                    ax.set_title(title, fontsize=14, fontweight='bold')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"创建热力图失败: {e}")
            return None

    def _create_placeholder_chart(self, title, message):
        """创建占位符图表"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            ax.text(0.5, 0.5, message, ha='center', va='center',
                   transform=ax.transAxes, fontsize=16,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"创建占位符图表失败: {e}")
            return None

    def _add_basic_content(self, doc, sorted_data, total_roads, level_counts, time_range):
        """添加基础内容（回退方案）"""
        self.log_message("📄 使用基础内容模板")

        # 基础的四个部分
        self._add_summary_section(doc, total_roads, level_counts, time_range)

        section2_title = doc.add_heading("二、监测道路汇总表", level=2)
        self.set_heading_style(section2_title)
        self._add_road_table_section(doc, sorted_data)

        section3_title = doc.add_heading("三、街道监测统计", level=2)
        self.set_heading_style(section3_title)
        self._add_street_table_section(doc, sorted_data)

        section4_title = doc.add_heading("四、差等级道路情况", level=2)
        self.set_heading_style(section4_title)
        self._add_poor_roads_section(doc, sorted_data, total_roads)

    def _format_time_string(self, time_value):
        """格式化时间字符串"""
        try:
            if pd.notna(time_value):
                if isinstance(time_value, str):
                    if 'T' in time_value:
                        date_part = time_value.split('T')[0]
                        time_part = time_value.split('T')[1].split(':')[:2]
                        return f"{date_part} {':'.join(time_part)}"
                    elif ' ' in time_value:
                        parts = time_value.split(' ')
                        if len(parts) >= 2:
                            date_part = parts[0]
                            time_part = parts[1].split(':')[:2]
                            return f"{date_part} {':'.join(time_part)}"
                else:
                    return time_value.strftime('%Y-%m-%d %H:%M')
            return ""
        except:
            time_str = str(time_value).split('.')[0]
            if ':' in time_str:
                time_parts = time_str.split(':')
                if len(time_parts) >= 3:
                    return ':'.join(time_parts[:2])
            return time_str















    def _save_word_report(self, doc, selected_streets, sorted_data, filter_info, abnormal_stats, current_allowed_roads, photos_count, total_roads, original_records, street_list, photos_info):
        """保存Word报告"""
        try:
            # 获取保存路径
            output_path, save_location, output_filename = self.get_save_file_path(selected_streets)

            if output_path is None:
                # 用户取消了保存
                self.status_var.set("❌ 用户取消了保存")
                return False

            # 尝试保存文档，如果文件被占用则添加时间戳
            import time
            attempt = 0
            max_attempts = 5

            while attempt < max_attempts:
                try:
                    # 检查文件是否存在且被占用
                    if os.path.exists(output_path):
                        # 尝试重命名现有文件来测试是否被占用
                        try:
                            timestamp = int(time.time())
                            temp_name = f"{os.path.splitext(output_path)[0]}_temp_{timestamp}{os.path.splitext(output_path)[1]}"
                            os.rename(output_path, temp_name)
                            os.rename(temp_name, output_path)  # 重命名回来
                        except OSError:
                            # 文件被占用，生成新的文件名
                            timestamp = int(time.time())
                            base_name = os.path.splitext(output_filename)[0]
                            output_filename = f"{base_name}_{timestamp}.docx"
                            if self.use_save_dialog_var.get():
                                output_path = os.path.join(os.path.dirname(output_path), output_filename)
                            else:
                                output_path = output_filename
                            attempt += 1
                            continue

                    # 尝试保存文档
                    doc.save(output_path)
                    break  # 保存成功，退出循环

                except PermissionError:
                    # 权限错误，生成新的文件名
                    timestamp = int(time.time())
                    base_name = os.path.splitext(output_filename)[0]
                    output_filename = f"{base_name}_{timestamp}.docx"
                    if self.use_save_dialog_var.get():
                        output_path = os.path.join(os.path.dirname(output_path), output_filename)
                    else:
                        output_path = output_filename
                    attempt += 1
                    if attempt >= max_attempts:
                        # 最后一次尝试，使用更简单的文件名
                        simple_filename = f"{self.selected_district}报告_{timestamp}.docx"
                        if self.use_save_dialog_var.get():
                            simple_path = os.path.join(os.path.dirname(output_path), simple_filename)
                        else:
                            simple_path = simple_filename
                        doc.save(simple_path)
                        output_filename = simple_filename
                        output_path = simple_path
                        break
                except Exception as e:
                    # 其他错误
                    if attempt >= max_attempts - 1:
                        raise e
                    timestamp = int(time.time())
                    base_name = os.path.splitext(output_filename)[0]
                    output_filename = f"{base_name}_{timestamp}.docx"
                    if self.use_save_dialog_var.get():
                        output_path = os.path.join(os.path.dirname(output_path), output_filename)
                    else:
                        output_path = output_filename
                    attempt += 1

            # 检查是否使用了备用文件名
            if "_" in output_filename.split(".")[-2] and output_filename.split(".")[-2][-10:].isdigit():
                file_note = "\n⚠️ 注意：由于原文件名被占用，已自动生成新的文件名"
            else:
                file_note = ""

            message = f"""📄 Word报告生成成功！

📁 文件名: {output_filename}
📂 保存位置: {save_location}
🏙️ 分析区域: {self.selected_district}
🏘️ 包含街道: {street_list}
🛣️ 道路数量: {total_roads:,} 条道路
📊 原始数据: {original_records:,} 条记录
📈 数据处理: 按道路透视分析，按尘负荷从小到大排序{filter_info}{photos_info}

报告已保存成功！{file_note}"""

            messagebox.showinfo("Word报告生成成功", message)
            self.log_message(f"✅ Word报告保存成功: {output_filename}")
            return True

        except Exception as e:
            self.log_message(f"❌ Word报告保存失败: {str(e)}")
            return False

    def _save_pdf_report(self, data, selected_streets, template_name):
        """保存PDF报告"""
        try:
            if not PDF_AVAILABLE:
                messagebox.showwarning("功能不可用", "PDF导出功能需要安装reportlab库")
                return False

            # 获取保存路径
            from datetime import datetime
            current_date = datetime.now().strftime("%Y.%m.%d")
            street_names_short = "、".join(selected_streets[:2])
            if len(selected_streets) > 2:
                street_names_short += f"等{len(selected_streets)}个街道"

            default_filename = f"北京市{self.selected_district}尘负荷监测结果报告（{current_date}）.pdf"

            file_path = filedialog.asksaveasfilename(
                title="保存PDF报告",
                defaultextension=".pdf",
                filetypes=[("PDF文档", "*.pdf"), ("所有文件", "*.*")],
                initialfile=default_filename
            )

            if not file_path:
                return False

            # 生成PDF报告
            success = self.pdf_generator.generate_pdf_report(data, file_path, template_name)

            if success:
                self.log_message(f"✅ PDF报告保存成功: {os.path.basename(file_path)}")
                messagebox.showinfo("PDF生成成功", f"PDF报告已保存至：\n{file_path}")
                return True
            else:
                self.log_message("❌ PDF报告生成失败")
                return False

        except Exception as e:
            self.log_message(f"❌ PDF报告保存失败: {str(e)}")
            messagebox.showerror("PDF生成失败", f"生成PDF报告时出错：\n{str(e)}")
            return False

    def generate_charts(self):
        """生成并展示图表，可单独下载"""
        if plt is None or FigureCanvasTkAgg is None:
            messagebox.showwarning("缺少依赖", "未安装 matplotlib，无法生成图表")
            return

        if self.df is None:
            messagebox.showerror("错误", "请先加载数据并选择街道")
            return

        selected_streets = self.get_selected_streets()
        if not selected_streets:
            messagebox.showerror("错误", "请先选择街道")
            return

        self.log_message("🎨 开始生成图表分析...")
        self.log_message(f"📊 目标区域: {self.selected_district}, 已选街道: {', '.join(selected_streets)}")

        # 与 generate_report 中的筛选逻辑保持一致（简化版）
        district_reference_streets = self.district_streets.get(self.selected_district, [])
        matched_data_streets = []
        for data_street in self.df['所属街道'].unique():
            matched_street = self.smart_street_match(data_street, district_reference_streets)
            if matched_street in selected_streets:
                matched_data_streets.append(data_street)

        data_df = self.df[self.df['所属街道'].isin(matched_data_streets)].copy()
        if data_df.empty:
            messagebox.showerror("错误", "选定街道没有数据")
            return

        self.log_message(f"📋 数据街道匹配完成，共找到 {len(matched_data_streets)} 个匹配的数据街道")

        # ======== 按与生成报告相同的流程处理数据 ========
        current_allowed_roads = self.allowed_roads.get(self.selected_district, set())
        if current_allowed_roads:
            data_df = data_df[data_df['道路名称'].isin(current_allowed_roads)].copy()
            self.log_message(f"🛣️ 道路筛选: {len(data_df)} 条记录 (使用道路匹配表)")

        # 标准化街道名称
        street_mapping = {}
        for data_street in data_df['所属街道'].unique():
            matched_street = self.smart_street_match(data_street, district_reference_streets)
            if matched_street and matched_street in selected_streets:
                street_mapping[data_street] = matched_street
        data_df['标准街道名称'] = data_df['所属街道'].map(street_mapping)
        data_df = data_df[data_df['标准街道名称'].notna()].copy()

        # 异常值处理
        if self.process_zero_values_var.get():
            data_df, _ = self.process_abnormal_dust_values(data_df)
            self.log_message("⚙️ 已处理异常尘负荷值")
        else:
            self.log_message("⏭️ 已跳过异常尘负荷值处理")

        # 重复道路处理
        if self.remove_duplicates_var.get():
            tmp_df = data_df.copy()
            tmp_df['所属街道'] = tmp_df['标准街道名称']
            data_df = self.remove_duplicate_roads(tmp_df)
            self.log_message("⚙️ 已处理重复道路")
        else:
            self.log_message("⏭️ 已跳过重复道路处理")

        # 透视：按街道/道路平均尘负荷
        pivot_data = data_df.groupby(['标准街道名称', '道路名称']).agg({'尘负荷': 'mean'}).reset_index()
        pivot_data['等级'] = pivot_data['尘负荷'].apply(self.get_dust_level)
        pivot_data = pivot_data.rename(columns={'标准街道名称': '所属街道'})

        # 基本统计
        level_counts = pivot_data['等级'].value_counts()
        self.log_message(f"📈 等级分布：优({level_counts.get('优', 0)}条), 良({level_counts.get('良', 0)}条), 中({level_counts.get('中', 0)}条), 差({level_counts.get('差', 0)}条)")

        # 差等级道路
        poor_roads_stats = pivot_data[pivot_data['等级'] == '差'].sort_values('尘负荷', ascending=False).head(10).set_index('道路名称')['尘负荷']
        if not poor_roads_stats.empty:
            self.log_message(f"⚠️ 发现 {len(pivot_data[pivot_data['等级'] == '差'])} 条差等级道路")

        # --- 创建图表窗口 ---
        self.log_message("🖼️ 创建图表窗口...")
        chart_win = tk.Toplevel(self.root)
        chart_win.title("尘负荷统计图表预览")

        # 先隐藏窗口，避免闪烁
        chart_win.withdraw()

        # 设置窗口属性
        window_width = 900
        window_height = 720

        # 计算屏幕中心位置
        screen_width = chart_win.winfo_screenwidth()
        screen_height = chart_win.winfo_screenheight()
        x_pos = (screen_width - window_width) // 2
        y_pos = (screen_height - window_height) // 2

        # 直接设置窗口大小和位置
        chart_win.geometry(f"{window_width}x{window_height}+{x_pos}+{y_pos}")

        # 完成所有设置后再显示窗口
        chart_win.update_idletasks()
        chart_win.deiconify()

        # 确保图表窗口显示在前台
        chart_win.lift()
        chart_win.focus_force()

        # 使用 Notebook 分页
        try:
            from tkinter import ttk as ttk_local
            notebook = ttk_local.Notebook(chart_win)
            notebook.pack(fill=tk.BOTH, expand=True)
        except Exception:
            notebook = None

        figs = []

        # 1. 街道平均尘负荷 Top10 柱状图
        self.log_message("📊 生成街道平均尘负荷 Top10 图表...")
        fig1, ax1 = plt.subplots(figsize=(6, 4))
        street_stats_chart = pivot_data.groupby('所属街道')['尘负荷'].mean().sort_values(ascending=False).head(10)
        streets = street_stats_chart.index[::-1]
        vals = street_stats_chart.values[::-1]

        # 颜色映射
        def _get_color(val):
            th = Config.DUST_LEVEL_THRESHOLDS
            if val <= th['优']:
                return '#1919FF'
            elif val <= th['良']:
                return '#FFFF00'
            elif val <= th['中']:
                return '#FF7E00'
            else:
                return '#FF0000'

        colors = [_get_color(v) for v in vals]

        bars = ax1.barh(streets, vals, color=colors)
        ax1.set_title('街道平均尘负荷 Top10')
        ax1.set_xlabel('平均尘负荷 (克/平方米)')

        # 添加数值标签
        for bar, val in zip(bars, vals):
            ax1.text(val + 0.01, bar.get_y() + bar.get_height()/2, f"{val:.2f}", va='center', ha='left', fontsize=10, fontname='Times New Roman')

        ax1.set_xlim(0, max(vals)*1.15)
        fig1.tight_layout(rect=[0, 0, 0.98, 0.95])
        figs.append(fig1)

        # 2. 等级饼图
        self.log_message("📊 生成道路等级占比饼图...")
        fig2, ax2 = plt.subplots(figsize=(6, 4))
        labels = ['优', '良', '中', '差']
        values = [level_counts.get(l, 0) for l in labels]
        ax2.pie(values, labels=labels, autopct='%1.1f%%', colors=['#1919FF', '#FFFF00', '#FF7E00', '#FF0000'])
        ax2.set_title('道路等级占比（饼图）')
        fig2.tight_layout(rect=[0, 0, 1, 0.95])
        figs.append(fig2)

        # 3. 差等级道路散点图（优化版）
        self.log_message("📊 生成差等级道路散点分布图...")
        fig3, ax3 = plt.subplots(figsize=(10, 6))  # 增大图表尺寸

        poor_all = pivot_data[pivot_data['等级']=='差'].copy()
        if poor_all.empty:
            self.log_message("✅ 未发现差等级道路，散点图为空")
            ax3.text(0.5, 0.5, '无差等级道路', ha='center', va='center', fontsize=14)
            ax3.set_xlim(0, 1)
            ax3.set_ylim(0, 1)
        else:
            self.log_message(f"⚙️ 处理 {len(poor_all)} 条差等级道路数据...")

            import numpy as np

            # 按街道分组并排序
            streets_scatter = poor_all['所属街道'].unique()
            streets_scatter = sorted(streets_scatter)  # 按字母顺序排序
            self.log_message(f"📍 差等级道路分布在 {len(streets_scatter)} 个街道")

            # 为每个街道分配y位置，留出足够间距
            street_spacing = 1.0  # 街道间距
            y_map = {st: i * street_spacing for i, st in enumerate(streets_scatter)}

            # 处理每个街道的数据点
            all_x_vals = []
            all_y_vals = []
            all_roads = []
            all_dust_vals = []

            for street in streets_scatter:
                street_data = poor_all[poor_all['所属街道'] == street].copy()
                base_y = y_map[street]

                # 按尘负荷值排序，便于处理重叠
                street_data = street_data.sort_values('尘负荷')

                # 为同一街道的道路添加y轴抖动，避免重叠
                n_roads = len(street_data)
                if n_roads == 1:
                    y_offsets = [0]
                else:
                    # 在±0.3范围内均匀分布
                    y_offsets = np.linspace(-0.3, 0.3, n_roads)

                for i, (_, row) in enumerate(street_data.iterrows()):
                    all_x_vals.append(row['尘负荷'])
                    all_y_vals.append(base_y + y_offsets[i])
                    all_roads.append(row['道路名称'])
                    all_dust_vals.append(row['尘负荷'])

            # 绘制散点
            ax3.scatter(all_x_vals, all_y_vals, c="#FF0000", marker='o',
                       s=100, edgecolors='black', linewidths=0.8, alpha=0.8)
            self.log_message(f"📌 绘制散点完成，共 {len(all_x_vals)} 个点")

            # 改进的标注排布算法 - 更智能地避免文字重叠
            try:
                from adjustText import adjust_text
                self.log_message(f"📑 使用 adjustText 库进行智能标签排布")

                # 先创建所有文本标签对象
                texts = []
                for x, y, road, dust_val in zip(all_x_vals, all_y_vals, all_roads, all_dust_vals):
                    # 设置初始标签位置在点右侧
                    text = ax3.text(x + 0.05, y, f"{road}\n{dust_val:.2f} 克/平方米",
                                   fontsize=9, color="#CC0000", fontweight='bold',
                                   ha='left', va='center',
                                   bbox=dict(boxstyle="round,pad=0.2", facecolor="white",
                                          edgecolor="#FF0000", alpha=0.8))
                    texts.append(text)

                # 尝试使用adjust_text进行智能排布
                adjust_text(texts,
                          arrowprops=dict(arrowstyle="-", color="#FF0000", lw=0.8),
                          expand_points=(1.2, 1.4),
                          force_points=(0.1, 0.25))
            except Exception as e:
                # 如果adjust_text失败，回退到手动避免重叠的方法
                self.log_message(f"⚠️ 智能标注排布失败: {e}，使用备用方法")
                for text in texts:
                    text.remove()  # 移除所有文本

                # 手动排布算法
                self.log_message(f"🔄 应用手动标签排布算法")
                used_positions = []  # (x, y)位置记录

                for x, y, road, dust_val in zip(all_x_vals, all_y_vals, all_roads, all_dust_vals):
                    # 初始标签位置
                    label_x = x + 0.1
                    label_y = y

                    # 检查是否与已有标签重叠
                    overlap = True
                    attempts = 0
                    directions = [(0, 0.1), (0.1, 0), (0, -0.1), (0.1, 0.1), (0.1, -0.1)]

                    while overlap and attempts < 20:
                        overlap = False
                        for used_x, used_y in used_positions:
                            # 计算标签之间的距离
                            if abs(label_x - used_x) < 0.2 and abs(label_y - used_y) < 0.2:
                                # 尝试移动标签
                                dx, dy = directions[attempts % len(directions)]
                                label_x += dx
                                label_y += dy
                                overlap = True
                                break
                        attempts += 1

                    # 添加当前位置到已用列表
                    used_positions.append((label_x, label_y))

                    # 添加标签和箭头
                    ax3.annotate(f"{road}\n{dust_val:.2f} 克/平方米",
                               xy=(x, y), xytext=(label_x, label_y),
                               fontsize=9, color="#CC0000", fontweight='bold',
                               ha='left', va='center',
                               bbox=dict(boxstyle="round,pad=0.2", facecolor="white",
                                       edgecolor="#FF0000", alpha=0.8),
                               arrowprops=dict(arrowstyle='->', color="#FF0000", lw=0.8))

            # 设置y轴标签和刻度
            ax3.set_yticks([y_map[st] for st in streets_scatter])
            ax3.set_yticklabels(streets_scatter, fontsize=10)

            # 添加街道分隔线
            for i, street in enumerate(streets_scatter[:-1]):
                y_line = (y_map[street] + y_map[streets_scatter[i+1]]) / 2
                ax3.axhline(y=y_line, color='gray', linestyle='--', alpha=0.3, linewidth=0.8)

            # 设置坐标轴
            ax3.set_xlabel('平均尘负荷 (克/平方米)', fontsize=12)
            ax3.set_ylabel('所属街道', fontsize=12)
            ax3.set_title('差等级道路散点分布图', fontsize=14, fontweight='bold')

            # 设置x轴范围
            left_bound = max(1.0, min(all_x_vals) - 0.1)
            right_bound = max(all_x_vals) * 1.5  # 为标注留出更多空间
            ax3.set_xlim(left_bound, right_bound)

            # 设置y轴范围
            ax3.set_ylim(-0.5, len(streets_scatter) * street_spacing - 0.5)

            # 添加网格
            ax3.grid(True, axis='x', alpha=0.3, linestyle='-', linewidth=0.5)

            # 添加统计信息
            total_poor_roads = len(poor_all)
            avg_dust = poor_all['尘负荷'].mean()
            max_dust = poor_all['尘负荷'].max()

            stats_text = f"差等级道路统计:\n总数: {total_poor_roads}条\n平均: {avg_dust:.2f} 克/平方米\n最高: {max_dust:.2f} 克/平方米"
            ax3.text(0.98, 0.98, stats_text, transform=ax3.transAxes,
                    fontsize=10, verticalalignment='top', horizontalalignment='right',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8, edgecolor='#666666'))

        fig3.tight_layout(rect=[0, 0, 0.98, 0.95])
        figs.append(fig3)

        # 4. 各街道尘负荷箱线+小提琴图
        self.log_message("📊 生成各街道尘负荷分布箱线+小提琴图...")
        fig4, ax4 = plt.subplots(figsize=(6, 4))
        box_data = []
        street_order = streets  # 使用前面 bar 图的顺序
        for st in street_order:
            box_data.append(pivot_data[pivot_data['所属街道'] == st]['尘负荷'])

        self.log_message(f"📊 生成小提琴图形: {len(street_order)} 个街道")
        parts = ax4.violinplot(box_data, positions=range(len(street_order)), showmeans=False, showmedians=True)
        for pc in parts['bodies']:
            pc.set_facecolor('#6baed6')
            pc.set_alpha(0.7)

        self.log_message(f"📊 叠加箱线图...")
        ax4.boxplot(box_data, positions=range(len(street_order)), widths=0.15, patch_artist=True,
                     boxprops=dict(facecolor="white", color="#3182bd"),
                     medianprops=dict(color="red"))
        ax4.set_xticks(range(len(street_order)))
        ax4.set_xticklabels(street_order, rotation=45, ha='right', fontsize=8)
        ax4.set_ylabel('尘负荷 (克/平方米)')
        ax4.set_title('各街道尘负荷分布（箱线+小提琴）')
        fig4.tight_layout(rect=[0,0,0.98,0.95])
        figs.append(fig4)
        # 5. Pareto 图（街道贡献）
        self.log_message("📊 生成街道尘负荷 Pareto 图...")
        from matplotlib.ticker import PercentFormatter
        fig5, ax5 = plt.subplots(figsize=(6,4))
        street_contrib = pivot_data.groupby('所属街道')['尘负荷'].sum().sort_values(ascending=False)
        streets_p = street_contrib.index
        vals_p = street_contrib.values
        cumulative = vals_p.cumsum()/vals_p.sum()*100

        self.log_message(f"📊 绘制柱状图: 街道尘负荷贡献率...")
        ax5.bar(streets_p, vals_p, color="#3498db")
        ax5.set_ylabel('尘负荷总量')
        ax5.set_xticklabels(streets_p, rotation=45, ha='right', fontsize=8)

        self.log_message(f"📊 绘制累积曲线: 累计贡献率...")
        ax5_twin = ax5.twinx()
        ax5_twin.plot(streets_p, cumulative, color='red', marker='o')
        ax5_twin.set_ylabel('累积百分比')
        ax5_twin.yaxis.set_major_formatter(PercentFormatter())
        ax5.set_title('街道尘负荷 Pareto (80/20)')
        ax5_twin.set_ylim(0, 100)
        fig5.tight_layout(rect=[0,0,0.98,0.95])
        figs.append(fig5)

        # 将图表嵌入 Notebook
        self.log_message("✅ 所有图表生成完成，正在嵌入界面...")
        titles = ['街道平均尘负荷 Top10', '道路等级占比（饼图）', '差等级道路散点', '尘负荷箱线+小提琴', 'Pareto 图']
        canvases = []


        for i, fig in enumerate(figs):
            frame = tk.Frame(notebook if notebook else chart_win)
            canvas = FigureCanvasTkAgg(fig, master=frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            canvases.append(canvas)
            if notebook:
                notebook.add(frame, text=titles[i])
            else:
                frame.pack(fill=tk.BOTH, expand=True)

        # 保存按钮
        def save_all():
            folder = filedialog.askdirectory(title="选择保存文件夹")
            if not folder:
                return
            for idx, fig in enumerate(figs):
                path = os.path.join(folder, f"{titles[idx]}.png")
                try:
                    fig.savefig(path, dpi=300)
                except Exception as e:
                    print(f"保存 {path} 失败: {e}")
            messagebox.showinfo("保存完成", "图表已保存到选定文件夹")

        btn_save = ttk.Button(chart_win, text="💾 保存全部图表", command=save_all, style='Modern.TButton')
        btn_save.pack(pady=6)

    # --------------------------------------------------
    def generate_tables(self):
        """生成透视后数据的 Excel 表格（道路汇总、街道统计、等级分析）"""
        if self.df is None:
            messagebox.showerror("错误", "请先加载数据文件")
            return

        selected_streets = self.get_selected_streets()
        if not selected_streets:
            messagebox.showerror("错误", "请至少选择一个街道")
            return

        # 与 generate_charts 相同的数据处理流程
        district_reference_streets = self.district_streets.get(self.selected_district, [])
        matched_data_streets = []
        for data_street in self.df['所属街道'].unique():
            matched_street = self.smart_street_match(data_street, district_reference_streets)
            if matched_street in selected_streets:
                matched_data_streets.append(data_street)

        data_df = self.df[self.df['所属街道'].isin(matched_data_streets)].copy()
        if data_df.empty:
            messagebox.showerror("错误", "选定街道没有数据")
            return

        # 道路筛选、异常值、重复道路处理
        current_allowed_roads = self.allowed_roads.get(self.selected_district, set())
        if current_allowed_roads:
            data_df = data_df[data_df['道路名称'].isin(current_allowed_roads)].copy()

        street_mapping = {}
        for data_street in data_df['所属街道'].unique():
            matched_street = self.smart_street_match(data_street, district_reference_streets)
            if matched_street and matched_street in selected_streets:
                street_mapping[data_street] = matched_street
        data_df['标准街道名称'] = data_df['所属街道'].map(street_mapping)
        data_df = data_df[data_df['标准街道名称'].notna()].copy()

        if self.process_zero_values_var.get():
            data_df, _ = self.process_abnormal_dust_values(data_df)

        if self.remove_duplicates_var.get():
            tmp_df = data_df.copy()
            tmp_df['所属街道'] = tmp_df['标准街道名称']
            data_df = self.remove_duplicate_roads(tmp_df)

        # 透视数据
        pivot_data = data_df.groupby(['标准街道名称', '道路名称']).agg({'尘负荷':'mean'}).reset_index()
        pivot_data['等级'] = pivot_data['尘负荷'].apply(self.get_dust_level)
        pivot_data = pivot_data.rename(columns={'标准街道名称':'所属街道'})

        street_stats = pivot_data.groupby('所属街道').agg({'道路名称':'count','尘负荷':'mean'}).reset_index()
        street_stats.columns = ['街道名称','道路数量（条）','平均尘负荷']
        street_stats['等级'] = street_stats['平均尘负荷'].apply(self.get_dust_level)

        level_counts = pivot_data['等级'].value_counts().reindex(['优','良','中','差']).fillna(0).astype(int)
        level_df = pd.DataFrame({'等级':level_counts.index,'道路数量':level_counts.values})

        # 保存文件
        save_path = filedialog.asksaveasfilename(
            title="保存表格",
            defaultextension=".xlsx",
            filetypes=[("Excel","*.xlsx")],
            initialfile=f"北京市{self.selected_district}尘负荷数据透视表_{datetime.now().strftime('%Y%m%d')}.xlsx")

        if not save_path:
            return

        try:
            with pd.ExcelWriter(save_path, engine='xlsxwriter') as writer:
                pivot_data.to_excel(writer, index=False, sheet_name='道路汇总')
                street_stats.to_excel(writer, index=False, sheet_name='街道统计')
                level_df.to_excel(writer, index=False, sheet_name='等级分布')
            messagebox.showinfo("成功", f"表格已保存至\n{save_path}")
        except Exception as e:
            messagebox.showerror("保存失败", str(e))

def main():
    """主函数"""
    root = tk.Tk()
    app = BeijingRoadDustAnalyzer(root)
    # 程序完全加载后将窗口居中显示
    app.center_window()
    root.mainloop()

if __name__ == "__main__":
    main()