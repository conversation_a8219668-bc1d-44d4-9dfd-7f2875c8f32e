# 北京市道路尘负荷数据分析系统

## 项目简介

北京市道路尘负荷数据分析系统是一个专门用于分析和可视化北京市道路尘埃负荷数据的桌面应用程序。该系统支持丰台区和经开区的数据分析，并预留了其他区域的接口，方便未来扩展。

## 功能特点

- **数据导入与处理**：支持Excel格式的道路尘负荷数据导入和处理
- **数据分析**：提供多种数据分析功能，包括统计分析和趋势分析
- **可视化图表**：使用Matplotlib和Plotly生成各类数据可视化图表
- **报告生成**：支持生成Word文档和PDF格式的分析报告
- **自定义报告模板**：通过ReportTemplateManager管理和应用不同的报告模板
- **高级图表生成**：通过AdvancedChartGenerator提供更丰富的数据可视化选项

## 系统要求

- Python 3.6+
- 依赖库：
  - pandas (>=1.3.0)
  - openpyxl (>=3.0.0)
  - python-docx (>=0.8.11)
  - matplotlib (>=3.3.0)
  - seaborn (>=0.11.0)
  - numpy (>=1.20.0)
  - reportlab (>=3.6.0)
  - plotly (>=5.0.0)
  - scikit-learn (>=1.0.0)
  - adjustText (>=0.7.3)

## 安装说明

1. 确保已安装Python 3.6或更高版本
2. 安装所需依赖库：
   ```
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行启动程序：
   ```
   python 启动程序.py
   ```
   或直接双击"启动程序.py"文件

2. 在应用程序界面中：
   - 导入道路尘负荷数据Excel文件
   - 选择分析区域（丰台区/经开区）
   - 使用各种分析工具进行数据处理
   - 生成可视化图表
   - 导出分析报告（Word/PDF格式）

## 数据文件

- **道路匹配表.xlsx**：包含道路信息的匹配数据，用于数据分析和处理

## 项目结构

- **beijing_road_dust_app.py**：主程序文件，包含应用程序的核心功能
- **启动程序.py**：简化的启动脚本，用于启动主程序
- **requirements.txt**：依赖库列表
- **道路匹配表.xlsx**：道路数据匹配表
- **icon.ico**：应用程序图标

## 主要类和功能

- **BeijingRoadDustAnalyzer**：主应用程序类，处理用户界面和核心功能
- **Config**：配置管理类
- **ReportTemplate**：报告模板类
- **ReportTemplateManager**：报告模板管理类
- **AdvancedChartGenerator**：高级图表生成类
- **PDFReportGenerator**：PDF报告生成类
- **MultiSelectCombobox**：多选下拉框组件

## 注意事项

- 首次运行时，请确保所有依赖库已正确安装
- PDF导出功能需要reportlab库支持，如未安装将不可用
- 使用中文字体显示需要相应的字体支持（SimHei、Microsoft YaHei或Arial Unicode MS）

## 开发者信息

本系统由北京市环境监测团队开发，用于道路尘负荷数据的专业分析和可视化。

## 许可证

© 2023 北京市环境监测团队，保留所有权利。