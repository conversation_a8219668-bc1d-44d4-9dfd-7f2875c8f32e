#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
北京市道路尘负荷数据分析系统
简化启动脚本
"""

import subprocess
import sys
import os

def main():
    """启动北京市扩展版"""
    print("🏙️ 北京市道路尘负荷数据分析系统")
    print("=" * 50)
    
    if not os.path.exists("beijing_road_dust_app.py"):
        print("❌ 找不到主程序文件: beijing_road_dust_app.py")
        input("按回车键退出...")
        return
    
    try:
        print("🚀 正在启动北京市扩展版...")
        subprocess.run([sys.executable, "beijing_road_dust_app.py"])
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
